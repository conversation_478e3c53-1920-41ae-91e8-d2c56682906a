# Lockheed Martin Amazon Web Services Collection (lmco.aws)

The Amazon Web Services (AWS) Collection includes a variety of Ansible content to help automate the management of AWS services. This collection supports integration of existing Terraform resource specifications that may be used to provision AWS resources alongside native Ansible resources. This collection uses the object-based role structure to conform to latest Galaxy Automation architecture.

## Authors

This collection is maintained by the [Lockheed Martin Galaxy Team](https://galaxy.pages.gitlab.global.lmco.com/documents/about/team/). Please see our [Feedback Page](https://galaxy.pages.gitlab.global.lmco.com/documents/feedback/) if you would like to pass along ideas or suggestions.

## Architecture

This collection uses the following structure for collection assets:

- Collection Namespace: lmco
- Collection Name: aws
- Collection Title: Amazon Web Services
- Role Prefix: aws_
- Variable prefix: *role_name*

This collection was developed to support a set of agile experiments to integrate Ansible and Terraform and determine level of complexity and ability to share data. This collection leveraged existing Terraform source from (https://gitlab.us.lmco.com/ECS/openshift/-/tree/master/terraform) to build and configure AWS resources. The result of the agile experiments were successful and let to development of roles in this collection.

This collection uses Ansible to orchestrate the Terraform resources and to dynamically generated the Terraform root directories. This eliminates the need to individual Terraform roots for each AWS account and environment as was the previous design. The root resource files are generated using Jinja templates following the original patterns, but Ansible parameters are used to generate the content and allow inbound configurations from the Galaxy collection. Parameter passing uses the variables section of the community.general.terraform and uses JSON as an intermediary format to avoid issues with string conversion that occurred during testing.

## Roles

The following standard object-based roles are defined in this collection

- [aws_common](roles/aws_common/README.md) - Standard common for all roles
- [aws_tfroot](roles/aws_tfroot/README.md) - Dynamically build the Terraform Root directory
- [aws_tfstate](roles/aws_tfstate/README.md) - Manage AWS resources required to support Terraform State
- [aws_tfvpc](roles/aws_tfvpc/README.md) - Manage AWS resources required for VPC configuration

## Installation

You can install the AWS collection with the Ansible Galaxy command line interface:

```sh
ansible-galaxy collection install lmco.aws                                                          # If sources configured
ansible-galaxy collection install git+https://gitlab.global.lmco.com/galaxy/internal/lmco/aws.git   # Direct from Galaxy repo
```

## AWS Exemptions

The following AWS exemptions must be in place before using this collection to configure AWS

- ec2:CreateTransitGateway
- ec2:DescribeTransitGateway
- ec2:DeleteTransitGateway
- ec2:CreateTransitGatewayVpcAttachment
- ec2:DescribeTransitGatewayVpcAttachment
- ec2:DeleteTransitGatewayVpcAttachment
- ec2:CreateVpnConnection
- ec2:DescribeVpnConnection
- ec2:DeleteVpnConnection

## Contributing

The content of this collection is made possible by the community of developer across Lockheed Martin. As part of the mission of the Galaxy program, community development is crucial for integration of existing assets and evolution of the capabilities. Please refer to the [CONTRIBUTING](CONTRIBUTING.md) document for more information.

## Compatibility

This collection has been tested with Ansible 2.15 and later versions.

## License

This collection is distributed under the Lockheed Martin License. See [LICENSE](LICENSE) for more information.
