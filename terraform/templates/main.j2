# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.j2                                                           #
# Version:                                                                        #
#               2024-01-25 WRC. Update for Galaxy Ansible                         #
#               2021-08-10 WRC. Initial                                           #
# Create Date:  2021-08-10                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               Established the terraform state this account and environment      #
#                                                                                 #
# ------------------------------------------------------------------------------- #

#-----------------------------------------------#
# Variable definition                           #
#-----------------------------------------------#
#variable "account"     { type = string }
#variable "region"      { type = string }
#variable "environment" { type = string }
variable "common_data" {}
 # type = object({
 #   account = string
  #  region = string
  #  environment = string
  #})
#}

#-----------------------------------------------#
# Gets the default AWS data using primary       #
# provider for this account.                    #
#-----------------------------------------------#
# aws_partition       = data.aws_partition.current.partition
data "aws_partition" "current" {
  provider = aws.{{ aws_region.name }}
}
data "aws_vpc" "available" {
  provider = aws.{{ aws_region.name }}
  state = "available"
}

#-----------------------------------------------#
# Modules Section.  Used to instantiate         #
# modules.                                      #
#-----------------------------------------------#
#module "common" {
#  source        = "{{ terraform_module_dir }}/common"
#  providers     = { aws = aws.us-east-1 }
#  account       = "{{ aws_account.id }}"
#  region        = "{{ aws_region.id }}"
#  environment   = "{{ aws_environment.id }}"
#}
module "tfstate" {
  source          = "{{ terraform_module_dir }}/tfstate"
  providers       = { aws = aws.{{ aws_region.name }} }
  common_data     = merge ({
    aws_partition = data.aws_partition.current.partition
    vpc           = data.aws_vpc.available
    vpc_id        = data.aws_vpc.available.id
  }, jsondecode(var.common_data) ) # Ansible passes in common_data as JSON string so convert to TF object
  # common_data     = common_data
}


# module "aws" {
#   source        = "../../../../modules/aws"
#  providers     = { aws = aws.us-east-1, aws.replica = aws.us-west-1 }
 #  account       = local.account
#  region        = local.region
 # environment   = local.environment
#  common_data   = module.common.common_data
#}

#-------------------------------------------------------------------------------------------#
# Outputs Section.  Used to create variables usable in other modules and configurations.    #
#-------------------------------------------------------------------------------------------#
#output "aws_out" {
#  value = module.aws.aws_out
#}
