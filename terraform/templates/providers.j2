# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        providers.j2                                                      #
# Version:                                                                        #
#               2024-01-25 WRC. Transformed to a Jinja template for Galaxy        #
#               2021-02-18 WRC. Initial                                           #
# Create Date:  2021-02-18                                                        #
# Author:       <PERSON><PERSON><PERSON>, Wayne (US) <<EMAIL>>                #
# Description:                                                                    #
#     Established the providers for this account and environment.  This was       #
#     split out as a separeate resource file because its common across all        #
#     environments and may be automated in the future.                            #
#                                                                                 #
# ------------------------------------------------------------------------------- #
provider "aws" {
  alias                     = "{{ aws_region.name }}"
  region                    = "{{ aws_region.name }}"
  profile                   = "aws"
  shared_credentials_files  = [ "{{ aws_credentials.filename }}" ]
  allowed_account_ids       = [ "{{ aws_account.id }}" ]
  max_retries               = {{aws_credentials.max_attempts}}
  insecure                  = false
}
