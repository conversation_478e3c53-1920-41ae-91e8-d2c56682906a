# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        293083252097\us-east-1\sn1\aws\terraform.tf                       #
# Version:                                                                        #
#               2021-08-10 WRC. Initial                                           #
# Create Date:  2021-08-10                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               Established the terraform state this account and environment      #
#                                                                                 #
# ------------------------------------------------------------------------------- #
terraform {

  # --------------------- #
  # Backend state data    #
  # --------------------- #
  backend "s3" {
    region                    = "{{ aws_region.name }}"
    bucket                    = "{{ aws_account.id }}-{{ aws_region.id }}-global-tfstate"
    dynamodb_table            = "{{ aws_region.id }}-global-tfstate"
    key                       = "{{ aws_region.id }}/{{ aws_environment.id }}/terraform.tfstate"  # Converge to single state file per environment
    shared_credentials_files  = [ "{{ aws_credentials.filename }}" ]
    profile                   = "tfstate"
    encrypt                   = true
    max_retries               = {{ aws_credentials.max_attempts }}
  }

  # --------------------- #
  # Common versions       #
  # --------------------- #
  required_version  = ">= {{ terraform_common.version }}"
  required_providers {
    {{ terraform_providers.archive.name }} = {
      source        = "{{ terraform_providers.archive.source }}/{{ terraform_providers.archive.name }}"
      version       = "{{ terraform_providers.archive.version }}"
    }
    {{ terraform_providers.aws.name }} = {
      source        = "{{ terraform_providers.aws.source }}/{{ terraform_providers.aws.name }}"
      version       = "{{ terraform_providers.aws.version }}"
    }
    {{ terraform_providers.local.name }} = {
      source        = "{{ terraform_providers.local.source }}/{{ terraform_providers.local.name }}"
      version       = "{{ terraform_providers.local.version }}"
    }
  }
}
