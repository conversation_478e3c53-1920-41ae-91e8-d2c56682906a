data "aws_caller_identity" "current" {}
data "aws_partition" "current" {}
data "aws_region" "current" {}
data "aws_vpc" "current" {}

################################################################################
# EKS
################################################################################

data "aws_eks_cluster_auth" "this" {
  name = var.cluster_name
}


################################################################################
# IAM
################################################################################

data "aws_iam_role" "federated_user" {
  name = var.radm_role
}

data "aws_iam_policy_document" "assume_role_policy" {
  statement {
    sid     = "AmazonEKSCluster"
    effect  = "Allow"
    actions = ["sts:AssumeRole"]
    principals {
      type        = "Service"
      identifiers = ["eks.amazonaws.com"]
    }

    principals {
      type        = "AWS"
      identifiers = ["${data.aws_iam_role.federated_user.arn}"]
    }
  }
}

data "aws_iam_policy" "common_deny" {
  name = "CommonDeny"
}

data "aws_iam_policy" "amazon_eks_cluster_policy" {
  name = "AmazonEKSClusterPolicy"
}