resource "aws_cloudwatch_log_group" "this" {
  count = var.create == true ? 1 : 0

  name              = "/aws/eks/${var.cluster_name}/cluster"
  kms_key_id        = aws_kms_key.this.arn
  retention_in_days = 1 # Set to 1 day retention due to high cost storage rate and aws required it is sent to S3. Data is sent to lmco splunk for more retention time.

  tags = merge(
    var.resource_tags,
    local.common_tags
  )

  lifecycle {
    ignore_changes = [
      tags["CreatedDate"]
    ]
  }

  depends_on = [
    aws_kms_key.this,
    aws_kms_key_policy.this
  ]
}