variable "create" {
  description = "Controls if EKS resources should be created (affects nearly all resources)"
  type        = bool
  default     = true
}


################################################################################
# Cluster
################################################################################

variable "cluster_name" {
  type        = string
  description = "The name of the cluster being created"
}

variable "cluster_version" {
  type        = string
  description = "The Kubernetes version to deploy on the EKS cluster"
}

variable "cluster_service_ipv4_cidr" {
  type        = string
  description = "The CIDR block to assign Kubernetes pod and service IP addresses from."
  default     = "**********/16"
}

variable "control_plane_subnet_ids" {
  type        = list(string)
  description = "The subnets in your VPC where the control plane may place elastic network interfaces (ENIs) to facilitate communication with your cluster."
}

variable "radm_role" {
  type        = string
  description = "The name of the radm role that will assume the cluster admin role"
}

variable "resource_tags" {
  type        = map(string)
  description = "Additional Resource Tags"
  default     = {}
}


################################################################################
# EKS Addons
################################################################################

variable "cluster_addons" {
  type        = any
  description = "Map of cluster addon configurations to enable for the cluster. Addon name can be the map keys or set with `name`. The `before_compute` flags is always required for the vpc-cni. You can change the default configuration of the add-ons and update them when desired. More AWS EKS addons can be found here: https://docs.aws.amazon.com/eks/latest/userguide/eks-add-ons.html"
  default = {
    aws-ebs-csi-driver = {
    }
    coredns = {
    }
    kube-proxy = {
    }
    vpc-cni = {
      before_compute = true
    }
  }
}
variable "cluster_addons_timeouts" {
  description = "Create, update, and delete timeout configurations for the cluster addons"
  type        = map(string)
  default = {
  }
}

variable "dataplane_wait_duration" {
  description = "Duration to wait after the EKS cluster has become active before creating the dataplane components (EKS managed nodegroup(s), self-managed nodegroup(s), Fargate profile(s))"
  type        = string
  default     = "30s"
}


################################################################################
# EKS Managed Node Group
################################################################################

variable "eks_managed_node_groups" {
  description = "Map of EKS managed node group definitions to create"
  type        = any
  default     = {}
}

variable "eks_managed_node_group_defaults" {
  description = "Map of EKS managed node group default configurations"
  type        = any
  default     = {}
}
variable "https_proxy" {
  type        = string
  default     = "http://proxy-zsgov.external.lmco.com:80"
  description = "The https proxy to use. Defaults to \"\"."
}

variable "lmco_cacert_download_url" {
  type        = string
  default     = "https://crl.external.lmco.com/trust/pem/combined/Combined_pem.pem"
  description = "The url to get the Lockheed Martin certificate on EC2 node startup"
}

################################################################################
# Required variables for FORCE Tagging
################################################################################

variable "business_area" {
  type        = string
  description = "Business area full name (ex: Enterprise Operations)"
  validation {
    condition     = contains(["Enterprise Operations", "Aeronautics Company", "Missiles and Fire Control", "Rotary and Mission Systems", "Space"], var.business_area)
    error_message = "Value of var.business_area should be \"Enterprise Operations\", \"Aeronautics Company\", \"Missiles and Fire Control\", \"Rotary and Mission Systems\", or \"Space\"."
  }
}

variable "common_id" {
  type        = string
  description = "The CommonID associated with this instance"

  validation {
    condition     = can(regex("\\d{9}", var.common_id))
    error_message = "CommonID should be a 9 digit string."
  }
}

variable "dr_criticality" {
  type        = number
  description = "Determine resource’s DR Criticality level based on RPO and RTO requirements. Options are 0, 1, 2, or 3"
  validation {
    condition     = contains([0, 1, 2, 3], var.dr_criticality)
    error_message = "Value of var.dr_criticality is not Valid. Options are 0, 1, 2, or 3."
  }
}

variable "environment" {
  type        = string
  description = "Options are prd, test, dev, sandbox, qas"
  default     = ""
  validation {
    condition     = contains(["prd", "test", "dev", "sbx", "qas"], var.environment)
    error_message = "Value of  var.environment is not Valid. Options are prd, test, dev, sbx, qas."
  }
}

variable "optout" {
  type        = string
  description = "Used to support the opting out of cost optimization automation. Options are vmrightsizing, vmshutdown, vminstancescheduler (or a combination of them)"
  default     = "none"
  validation {
    condition = anytrue([for which_outout in ["vmrightsizing", "vmshutdown", "vminstancescheduler"] :
      strcontains(var.optout, which_outout)
    ])
    error_message = "Value of var.optout is not Valid. Options are vmrightsizing, vmshutdown, vminstancescheduler (or a combination of them)."
  }
}
