resource "aws_security_group" "cluster" {
  name        = "eks-cluster-sg-${var.cluster_name}"
  vpc_id      = data.aws_vpc.current.id
  description = "Custom EKS Cluster Control Plane Security Group"

  tags = merge(
    var.resource_tags,
    local.common_tags,
    {
      Name = "eks-cluster-sg-${var.cluster_name}"
    },
  )

  lifecycle {
    ignore_changes = [
      tags["CreatedDate"]
    ]
  }
}

resource "aws_security_group_rule" "egress" {
  security_group_id = aws_security_group.cluster.id
  type              = "egress"
  description       = "Allow any traffic from the nodes"
  from_port         = 0
  to_port           = 0
  protocol          = -1
  cidr_blocks       = ["0.0.0.0/0"]
}

resource "aws_security_group_rule" "ingress_1" {
  security_group_id = aws_security_group.cluster.id
  type              = "ingress"
  description       = "Allow all TCP traffic ports (0-65535) except 22 and 3389"
  from_port         = 0
  to_port           = 21
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
}

resource "aws_security_group_rule" "ingress_2" {
  security_group_id = aws_security_group.cluster.id
  type              = "ingress"
  description       = "Allow all TCP traffic ports (0-65535) except 22 and 3389"
  from_port         = 23
  to_port           = 3388
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
}

resource "aws_security_group_rule" "ingress_3" {
  security_group_id = aws_security_group.cluster.id
  type              = "ingress"
  description       = "Allow all TCP traffic ports (0-65535) except 22 and 3389"
  from_port         = 3390
  to_port           = 65535
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
}

# These rules are attached to both the Control Plane EKS Cluster and Node Group EC2s
resource "aws_security_group_rule" "ingress_nodeport" {
  security_group_id = aws_eks_cluster.this[0].vpc_config[0].cluster_security_group_id
  type              = "ingress"
  description       = "Allow NodePort traffic to the nodes"
  from_port         = 30000
  to_port           = 32768
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
}

resource "aws_security_group_rule" "ingress_https" {
  security_group_id = aws_eks_cluster.this[0].vpc_config[0].cluster_security_group_id
  type              = "ingress"
  description       = "Allow HTTPS traffic to the nodes"
  from_port         = 443
  to_port           = 443
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
}
