################################################################################
# Cluster
################################################################################

output "cluster_arn" {
  description = "The Amazon Resource Name (ARN) of the cluster"
  value       = try(aws_eks_cluster.this[0].arn, null)
}

output "cluster_certificate_authority_data" {
  description = "Base64 encoded certificate data required to communicate with the cluster"
  value       = try(aws_eks_cluster.this[0].certificate_authority[0].data, null)
}

output "cluster_endpoint" {
  description = "Endpoint for your Kubernetes API server"
  value       = try(aws_eks_cluster.this[0].endpoint, null)
}

output "cluster_name" {
  description = "The name of the EKS cluster"
  value       = try(aws_eks_cluster.this[0].name, "")
}

output "cluster_oidc_issuer_url" {
  description = "The URL on the EKS cluster for the OpenID Connect identity provider"
  value       = try(aws_eks_cluster.this[0].identity[0].oidc[0].issuer, null)
}

output "cluster_platform_version" {
  description = "Platform version for the cluster"
  value       = try(aws_eks_cluster.this[0].platform_version, null)
}

output "cluster_primary_security_group_id" {
  description = "Cluster security group that was created by Amazon EKS for the cluster. Managed node groups use this security group for control-plane-to-data-plane communication. Referred to as 'Cluster security group' in the EKS console"
  value       = try(aws_eks_cluster.this[0].vpc_config[0].cluster_security_group_id, null)
}

output "cluster_status" {
  description = "Status of the EKS cluster. One of `CREATING`, `ACTIVE`, `DELETING`, `FAILED`"
  value       = try(aws_eks_cluster.this[0].status, null)
}

output "cluster_version" {
  description = "The Kubernetes version for the cluster"
  value       = try(aws_eks_cluster.this[0].version, null)
}
output "node_group_aws_eks_node_group_name" {
  description = "Name of the aws node group for to the cluster"
  value       = try(module.eks_managed_node_group[*].aws_eks_node_group_name, null)
}


################################################################################
# EKS Addons
################################################################################

output "cluster_addons" {
  description = "Map of attribute maps for all EKS cluster addons enabled"
  value       = merge(aws_eks_addon.this, aws_eks_addon.before_compute)
}


################################################################################
# KMS Key
################################################################################

output "kms_key_alias" {
  description = "The KMS key alias"
  value       = try(aws_kms_alias.this.name, null)
}

output "kms_key_arn" {
  description = "Amazon Resource Name (ARN) of the KMS key"
  value       = try(aws_kms_key.this.arn, null)
}

output "kms_key_id" {
  description = "ID of the KMS key"
  value       = try(aws_kms_key.this.id, null)
}

output "node_group_kms_key_id" {
  description = "ID of the KMS key of the Node Groups for the EKS Cluster"
  value       = try(module.eks_managed_node_group[*].kms_key_id, null)
}

output "node_group_kms_key_arn" {
  description = "Amazon Resource Name (ARN) of the KMS key of the Node Groups for the EKS Cluster"
  value       = try(module.eks_managed_node_group[*].kms_key_arn, null)
}

output "node_group_kms_key_alias" {
  description = "The KMS key alias of the Node Groups for the EKS Cluster"
  value       = try(module.eks_managed_node_group[*].kms_key_alias, null)
}


################################################################################
# Cluster Security Group
################################################################################

output "cluster_security_group_arn" {
  description = "Amazon Resource Name (ARN) of the cluster security group"
  value       = try(aws_security_group.cluster.arn, null)
}

output "cluster_security_group_id" {
  description = "ID of cluster security group"
  value       = try(aws_security_group.cluster.id, null)
}


################################################################################
# IAM Role
################################################################################
output "access_policy_associations" {
  description = "Eks cluster access policy associations created and their attributes"
  value       = try(aws_eks_access_policy_association.cluster_admin, null)
}

output "cluster_iam_role_arn" {
  description = "IAM role Amazon Resource Name (ARN) of the EKS Cluster"
  value       = try(aws_iam_role.this.arn, null)
}

output "cluster_iam_role_name" {
  description = "IAM role name of the EKS Cluster"
  value       = try(aws_iam_role.this.name, null)
}

output "node_group_aws_iam_role_name" {
  description = "IAM role name of the Node Groups for the EKS Cluster"
  value       = try(module.eks_managed_node_group[*].aws_iam_role_name, null)
}

output "node_group_aws_iam_role_arn" {
  description = "IAM role Amazon Resource Name (ARN) of the Node Groups for the EKS Cluster"
  value       = try(module.eks_managed_node_group[*].aws_iam_role_arn, null)
}

output "node_group_aws_iam_role_policy_attachment_ec2_container_registry_readonly" {
  description = "EC2 Container Registry Readonly Policy definition, to be consumed as a resource in IAM.tf"
  value       = try(module.eks_managed_node_group[*].aws_iam_role_policy_attachment_ec2_container_registry_readonly, null)
}

output "node_group_aws_iam_role_policy_attachment_eks_worker_node_policy" {
  description = "EKS Worker Node Policy definition, to be consumed as a resource in IAM.tf"
  value       = try(module.eks_managed_node_group[*].aws_iam_role_policy_attachment_eks_worker_node_policy, null)
}

output "node_group_aws_iam_role_policy_attachment_eks_cni_policy" {
  description = "EKS CNI Policy definition, to be consumed as a resource in IAM.tf"
  value       = try(module.eks_managed_node_group[*].aws_iam_role_policy_attachment_eks_cni_policy, null)
}

output "node_group_aws_iam_role_policy_attachment_ec2_read_only_access" {
  description = "EC2 Read only access policy definition, to be consumed as a resource in IAM.tf"
  value       = try(module.eks_managed_node_group[*].aws_iam_role_policy_attachment_ec2_read_only_access, null)
}


################################################################################
# Launch Template
################################################################################

output "node_group_launch_templates_name" {
  description = "Name of the Node Groups Launch Template"
  value       = try(module.eks_managed_node_group[*].launch_templates_name, null)
}

output "node_group_launch_templates_id" {
  description = "ID of the Node Groups Launch Template"
  value       = try(module.eks_managed_node_group[*].launch_templates_id, null)
}

output "node_group_launch_templates_version" {
  description = "Version of the Node Groups Launch Template"
  value       = try(module.eks_managed_node_group[*].launch_templates_version, null)
}

output "node_groups" {
  description = "All node group info"
  value       = { for k, v in var.eks_managed_node_groups : k => v }
}
