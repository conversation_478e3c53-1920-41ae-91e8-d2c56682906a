<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | ~> 1.5.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~>5.33.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~>5.33.0 |
| <a name="provider_time"></a> [time](#provider\_time) | n/a |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_eks_managed_node_group"></a> [eks\_managed\_node\_group](#module\_eks\_managed\_node\_group) | ./modules/eks-managed-node-group | n/a |

## Resources

| Name | Type |
|------|------|
| [aws_cloudwatch_log_group.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudwatch_log_group) | resource |
| [aws_eks_access_entry.cluster_admin](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/eks_access_entry) | resource |
| [aws_eks_access_policy_association.cluster_admin](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/eks_access_policy_association) | resource |
| [aws_eks_addon.before_compute](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/eks_addon) | resource |
| [aws_eks_addon.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/eks_addon) | resource |
| [aws_eks_cluster.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/eks_cluster) | resource |
| [aws_iam_role.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role_policy_attachment.amazon_eks_cluster_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.common_deny](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_kms_alias.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/kms_alias) | resource |
| [aws_kms_key.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/kms_key) | resource |
| [aws_kms_key_policy.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/kms_key_policy) | resource |
| [aws_security_group.cluster](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group) | resource |
| [aws_security_group_rule.egress](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule) | resource |
| [aws_security_group_rule.ingress_1](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule) | resource |
| [aws_security_group_rule.ingress_2](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule) | resource |
| [aws_security_group_rule.ingress_3](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule) | resource |
| [aws_security_group_rule.ingress_https](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule) | resource |
| [aws_security_group_rule.ingress_nodeport](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group_rule) | resource |
| [time_sleep.this](https://registry.terraform.io/providers/hashicorp/time/latest/docs/resources/sleep) | resource |
| [aws_caller_identity.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/caller_identity) | data source |
| [aws_eks_addon_version.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/eks_addon_version) | data source |
| [aws_eks_cluster_auth.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/eks_cluster_auth) | data source |
| [aws_iam_policy.amazon_eks_cluster_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy) | data source |
| [aws_iam_policy.common_deny](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy) | data source |
| [aws_iam_policy_document.assume_role_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |
| [aws_iam_policy_document.kms](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |
| [aws_iam_role.federated_user](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_role) | data source |
| [aws_partition.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/partition) | data source |
| [aws_region.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/region) | data source |
| [aws_vpc.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/vpc) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_business_area"></a> [business\_area](#input\_business\_area) | Business area full name (ex: Enterprise Operations) | `string` | n/a | yes |
| <a name="input_cluster_addons"></a> [cluster\_addons](#input\_cluster\_addons) | Map of cluster addon configurations to enable for the cluster. Addon name can be the map keys or set with `name`. The `before_compute` flags is always required for the vpc-cni. You can change the default configuration of the add-ons and update them when desired. More AWS EKS addons can be found here: https://docs.aws.amazon.com/eks/latest/userguide/eks-add-ons.html | `any` | <pre>{<br>  "aws-ebs-csi-driver": {},<br>  "coredns": {},<br>  "kube-proxy": {},<br>  "vpc-cni": {<br>    "before_compute": true<br>  }<br>}</pre> | no |
| <a name="input_cluster_addons_timeouts"></a> [cluster\_addons\_timeouts](#input\_cluster\_addons\_timeouts) | Create, update, and delete timeout configurations for the cluster addons | `map(string)` | `{}` | no |
| <a name="input_cluster_name"></a> [cluster\_name](#input\_cluster\_name) | The name of the cluster being created | `string` | n/a | yes |
| <a name="input_cluster_service_ipv4_cidr"></a> [cluster\_service\_ipv4\_cidr](#input\_cluster\_service\_ipv4\_cidr) | The CIDR block to assign Kubernetes pod and service IP addresses from. | `string` | `"**********/16"` | no |
| <a name="input_cluster_version"></a> [cluster\_version](#input\_cluster\_version) | The Kubernetes version to deploy on the EKS cluster | `string` | n/a | yes |
| <a name="input_common_id"></a> [common\_id](#input\_common\_id) | The CommonID associated with this instance | `string` | n/a | yes |
| <a name="input_control_plane_subnet_ids"></a> [control\_plane\_subnet\_ids](#input\_control\_plane\_subnet\_ids) | The subnets in your VPC where the control plane may place elastic network interfaces (ENIs) to facilitate communication with your cluster. | `list(string)` | n/a | yes |
| <a name="input_create"></a> [create](#input\_create) | Controls if EKS resources should be created (affects nearly all resources) | `bool` | `true` | no |
| <a name="input_dataplane_wait_duration"></a> [dataplane\_wait\_duration](#input\_dataplane\_wait\_duration) | Duration to wait after the EKS cluster has become active before creating the dataplane components (EKS managed nodegroup(s), self-managed nodegroup(s), Fargate profile(s)) | `string` | `"30s"` | no |
| <a name="input_dr_criticality"></a> [dr\_criticality](#input\_dr\_criticality) | Determine resource’s DR Criticality level based on RPO and RTO requirements. Options are 0, 1, 2, or 3 | `number` | n/a | yes |
| <a name="input_eks_managed_node_group_defaults"></a> [eks\_managed\_node\_group\_defaults](#input\_eks\_managed\_node\_group\_defaults) | Map of EKS managed node group default configurations | `any` | `{}` | no |
| <a name="input_eks_managed_node_groups"></a> [eks\_managed\_node\_groups](#input\_eks\_managed\_node\_groups) | Map of EKS managed node group definitions to create | `any` | `{}` | no |
| <a name="input_environment"></a> [environment](#input\_environment) | Options are prd, test, dev, sandbox, qas | `string` | `""` | no |
| <a name="input_https_proxy"></a> [https\_proxy](#input\_https\_proxy) | The https proxy to use. Defaults to "". | `string` | `"http://proxy-zsgov.external.lmco.com:80"` | no |
| <a name="input_lmco_cacert_download_url"></a> [lmco\_cacert\_download\_url](#input\_lmco\_cacert\_download\_url) | The url to get the Lockheed Martin certificate on EC2 node startup | `string` | `"https://crl.external.lmco.com/trust/pem/combined/Combined_pem.pem"` | no |
| <a name="input_optout"></a> [optout](#input\_optout) | Used to support the opting out of cost optimization automation. Options are vmrightsizing, vmshutdown, vminstancescheduler (or a combination of them) | `string` | `"none"` | no |
| <a name="input_radm_role"></a> [radm\_role](#input\_radm\_role) | The name of the radm role that will assume the cluster admin role | `string` | n/a | yes |
| <a name="input_resource_tags"></a> [resource\_tags](#input\_resource\_tags) | Additional Resource Tags | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_access_policy_associations"></a> [access\_policy\_associations](#output\_access\_policy\_associations) | Eks cluster access policy associations created and their attributes |
| <a name="output_cluster_addons"></a> [cluster\_addons](#output\_cluster\_addons) | Map of attribute maps for all EKS cluster addons enabled |
| <a name="output_cluster_arn"></a> [cluster\_arn](#output\_cluster\_arn) | The Amazon Resource Name (ARN) of the cluster |
| <a name="output_cluster_certificate_authority_data"></a> [cluster\_certificate\_authority\_data](#output\_cluster\_certificate\_authority\_data) | Base64 encoded certificate data required to communicate with the cluster |
| <a name="output_cluster_endpoint"></a> [cluster\_endpoint](#output\_cluster\_endpoint) | Endpoint for your Kubernetes API server |
| <a name="output_cluster_iam_role_arn"></a> [cluster\_iam\_role\_arn](#output\_cluster\_iam\_role\_arn) | IAM role Amazon Resource Name (ARN) of the EKS Cluster |
| <a name="output_cluster_iam_role_name"></a> [cluster\_iam\_role\_name](#output\_cluster\_iam\_role\_name) | IAM role name of the EKS Cluster |
| <a name="output_cluster_name"></a> [cluster\_name](#output\_cluster\_name) | The name of the EKS cluster |
| <a name="output_cluster_oidc_issuer_url"></a> [cluster\_oidc\_issuer\_url](#output\_cluster\_oidc\_issuer\_url) | The URL on the EKS cluster for the OpenID Connect identity provider |
| <a name="output_cluster_platform_version"></a> [cluster\_platform\_version](#output\_cluster\_platform\_version) | Platform version for the cluster |
| <a name="output_cluster_primary_security_group_id"></a> [cluster\_primary\_security\_group\_id](#output\_cluster\_primary\_security\_group\_id) | Cluster security group that was created by Amazon EKS for the cluster. Managed node groups use this security group for control-plane-to-data-plane communication. Referred to as 'Cluster security group' in the EKS console |
| <a name="output_cluster_security_group_arn"></a> [cluster\_security\_group\_arn](#output\_cluster\_security\_group\_arn) | Amazon Resource Name (ARN) of the cluster security group |
| <a name="output_cluster_security_group_id"></a> [cluster\_security\_group\_id](#output\_cluster\_security\_group\_id) | ID of cluster security group |
| <a name="output_cluster_status"></a> [cluster\_status](#output\_cluster\_status) | Status of the EKS cluster. One of `CREATING`, `ACTIVE`, `DELETING`, `FAILED` |
| <a name="output_cluster_version"></a> [cluster\_version](#output\_cluster\_version) | The Kubernetes version for the cluster |
| <a name="output_kms_key_alias"></a> [kms\_key\_alias](#output\_kms\_key\_alias) | The KMS key alias |
| <a name="output_kms_key_arn"></a> [kms\_key\_arn](#output\_kms\_key\_arn) | Amazon Resource Name (ARN) of the KMS key |
| <a name="output_kms_key_id"></a> [kms\_key\_id](#output\_kms\_key\_id) | ID of the KMS key |
| <a name="output_node_group_aws_eks_node_group_name"></a> [node\_group\_aws\_eks\_node\_group\_name](#output\_node\_group\_aws\_eks\_node\_group\_name) | Name of the aws node group for to the cluster |
| <a name="output_node_group_aws_iam_role_arn"></a> [node\_group\_aws\_iam\_role\_arn](#output\_node\_group\_aws\_iam\_role\_arn) | IAM role Amazon Resource Name (ARN) of the Node Groups for the EKS Cluster |
| <a name="output_node_group_aws_iam_role_name"></a> [node\_group\_aws\_iam\_role\_name](#output\_node\_group\_aws\_iam\_role\_name) | IAM role name of the Node Groups for the EKS Cluster |
| <a name="output_node_group_aws_iam_role_policy_attachment_ec2_container_registry_readonly"></a> [node\_group\_aws\_iam\_role\_policy\_attachment\_ec2\_container\_registry\_readonly](#output\_node\_group\_aws\_iam\_role\_policy\_attachment\_ec2\_container\_registry\_readonly) | EC2 Container Registry Readonly Policy definition, to be consumed as a resource in IAM.tf |
| <a name="output_node_group_aws_iam_role_policy_attachment_ec2_read_only_access"></a> [node\_group\_aws\_iam\_role\_policy\_attachment\_ec2\_read\_only\_access](#output\_node\_group\_aws\_iam\_role\_policy\_attachment\_ec2\_read\_only\_access) | EC2 Read only access policy definition, to be consumed as a resource in IAM.tf |
| <a name="output_node_group_aws_iam_role_policy_attachment_eks_cni_policy"></a> [node\_group\_aws\_iam\_role\_policy\_attachment\_eks\_cni\_policy](#output\_node\_group\_aws\_iam\_role\_policy\_attachment\_eks\_cni\_policy) | EKS CNI Policy definition, to be consumed as a resource in IAM.tf |
| <a name="output_node_group_aws_iam_role_policy_attachment_eks_worker_node_policy"></a> [node\_group\_aws\_iam\_role\_policy\_attachment\_eks\_worker\_node\_policy](#output\_node\_group\_aws\_iam\_role\_policy\_attachment\_eks\_worker\_node\_policy) | EKS Worker Node Policy definition, to be consumed as a resource in IAM.tf |
| <a name="output_node_group_kms_key_alias"></a> [node\_group\_kms\_key\_alias](#output\_node\_group\_kms\_key\_alias) | The KMS key alias of the Node Groups for the EKS Cluster |
| <a name="output_node_group_kms_key_arn"></a> [node\_group\_kms\_key\_arn](#output\_node\_group\_kms\_key\_arn) | Amazon Resource Name (ARN) of the KMS key of the Node Groups for the EKS Cluster |
| <a name="output_node_group_kms_key_id"></a> [node\_group\_kms\_key\_id](#output\_node\_group\_kms\_key\_id) | ID of the KMS key of the Node Groups for the EKS Cluster |
| <a name="output_node_group_launch_templates_id"></a> [node\_group\_launch\_templates\_id](#output\_node\_group\_launch\_templates\_id) | ID of the Node Groups Launch Template |
| <a name="output_node_group_launch_templates_name"></a> [node\_group\_launch\_templates\_name](#output\_node\_group\_launch\_templates\_name) | Name of the Node Groups Launch Template |
| <a name="output_node_group_launch_templates_version"></a> [node\_group\_launch\_templates\_version](#output\_node\_group\_launch\_templates\_version) | Version of the Node Groups Launch Template |
| <a name="output_node_groups"></a> [node\_groups](#output\_node\_groups) | All node group info |
<!-- END_TF_DOCS -->