
module "eks_managed_node_group" {
  source   = "./modules/eks-managed-node-group"
  for_each = { for k, v in var.eks_managed_node_groups : k => v if var.create }
  create   = try(each.value.create, true)

  cluster_name             = var.cluster_name
  https_proxy              = var.https_proxy
  lmco_cacert_download_url = var.lmco_cacert_download_url
  radm_role                = var.radm_role
  resource_tags            = var.resource_tags

  volume_size           = try(each.value.volume_size, null)
  node_group_name       = try(each.value.node_group_name, null)
  node_group_subnet_ids = try(each.value.node_group_subnet_ids, null)

  #Required Tags
  common_id      = try(each.value.common_id, null)
  dr_criticality = try(each.value.dr_criticality, null)
  business_area  = try(each.value.business_area, null)
  environment    = try(each.value.environment, null)
  optout         = try(each.value.optout, null)

  depends_on = [aws_eks_cluster.this]
}


# This was primarily added to give addons enough time to create and configure themselves before the data plane compute resources are created.
resource "time_sleep" "this" {
  count = var.create ? 1 : 0

  create_duration = var.dataplane_wait_duration

  triggers = {
    cluster_name     = aws_eks_cluster.this[0].name
    cluster_endpoint = aws_eks_cluster.this[0].endpoint
    cluster_version  = aws_eks_cluster.this[0].version

    cluster_certificate_authority_data = aws_eks_cluster.this[0].certificate_authority[0].data
  }
}
