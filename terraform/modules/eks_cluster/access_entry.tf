resource "aws_eks_access_entry" "cluster_admin" {
  cluster_name      = aws_eks_cluster.this[0].name
  kubernetes_groups = ["cluster-admin-federated-group"]
  principal_arn     = data.aws_iam_role.federated_user.arn
}

resource "aws_eks_access_policy_association" "cluster_admin" {
  cluster_name  = aws_eks_cluster.this[0].name
  policy_arn    = local.eks_cluster_access_policy_arns.cluster_admin
  principal_arn = data.aws_iam_role.federated_user.arn

  access_scope {
    type       = "cluster"
    namespaces = []
  }

  depends_on = [aws_eks_access_entry.cluster_admin]
}
