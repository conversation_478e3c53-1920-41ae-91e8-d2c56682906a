resource "aws_eks_cluster" "this" {
  count = var.create == true ? 1 : 0

  name     = var.cluster_name
  role_arn = aws_iam_role.this.arn
  version  = var.cluster_version

  vpc_config {
    security_group_ids      = [aws_security_group.cluster.id]
    subnet_ids              = var.control_plane_subnet_ids
    endpoint_private_access = true
    endpoint_public_access  = false
  }

  access_config {
    authentication_mode                         = "API"
    bootstrap_cluster_creator_admin_permissions = false # Prevents any non-federated user from accessing cluster
  }

  # Add the encryption key to the cluster for encrypting secrets
  encryption_config {
    provider {
      key_arn = aws_kms_key.this.arn
    }
    resources = ["secrets"]
  }

  kubernetes_network_config {
    service_ipv4_cidr = var.cluster_service_ipv4_cidr
  }

  tags = merge(
    var.resource_tags,
    local.required_lmco_tags,
    local.common_tags,
    {
      Name        = var.cluster_name
      Description = "EKS Cluster provisioned by Terraform"
    }

  )

  lifecycle {
    ignore_changes = [
      tags["CreatedDate"]
    ]
  }

  enabled_cluster_log_types = ["api", "audit", "authenticator", "controllerManager", "scheduler"]

  depends_on = [
    aws_cloudwatch_log_group.this # This prevents collision by EKS service because it automatically gets created if a value for enabled_cluster_log_types is defined. 
  ]
}
