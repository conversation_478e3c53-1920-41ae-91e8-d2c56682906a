resource "aws_kms_key" "this" {
  description             = "EKS KMS ${var.cluster_name}"
  deletion_window_in_days = 30 # default value
  key_usage               = "ENCRYPT_DECRYPT"
  enable_key_rotation     = true
  tags                    = local.common_tags

  lifecycle {
    ignore_changes = [
      tags["CreatedDate"]
    ]
  }
}

resource "aws_kms_alias" "this" {
  name_prefix   = "alias/eks-${var.cluster_name}-"
  target_key_id = aws_kms_key.this.arn
}

resource "aws_kms_key_policy" "this" {
  key_id = aws_kms_key.this.id
  policy = data.aws_iam_policy_document.kms.json
}

data "aws_iam_policy_document" "kms" {
  statement {
    sid       = "Enable IAM User Permissions"
    effect    = "Allow"
    resources = ["*"]
    actions   = ["kms:*"]

    condition {
      test     = "StringLike"
      variable = "aws:PrincipalArn"
      values = [
        "${data.aws_iam_role.federated_user.arn}",
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }

    principals {
      type        = "*"
      identifiers = ["*"]
    }
  }

  statement {
    sid       = "Allow administration of the key"
    effect    = "Allow"
    resources = ["arn:${data.aws_partition.current.id}:kms:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:key/*"]

    actions = [
      "kms:CreateAlias",
      "kms:CreateGrant",
      "kms:CreateKey",
      "kms:DescribeKey",
      "kms:EnableKey",
      "kms:EnableKeyRotation",
      "kms:ListGrants",
      "kms:ListRetirableGrants",
      "kms:ListKeyPolicies",
      "kms:ListKeys",
      "kms:ListResourceTags",
      "kms:ListAliases",
      "kms:PutKeyPolicy",
      "kms:UpdateKeyDescription",
      "kms:UpdateAlias",
      "kms:RevokeGrant",
      "kms:RetireGrant",
      "kms:DisableKey",
      "kms:DisableKeyRotation",
      "kms:GetKeyPolicy",
      "kms:GetKeyRotationStatus",
      "kms:GetParametersForImport",
      "kms:DeleteAlias",
      "kms:DeleteImportedKeyMaterial",
      "kms:ImportKeyMaterial",
      "kms:TagResource",
      "kms:UntagResource",
      "kms:ScheduleKeyDeletion",
      "kms:CancelKeyDeletion",
      "kms:GenerateDataKeyWithoutPlaintext",
      "kms:GenerateRandom",
    ]

    condition {
      test     = "StringLike"
      variable = "aws:PrincipalArn"

      values = [
        "${data.aws_iam_role.federated_user.arn}",
        "${data.aws_caller_identity.current.arn}",
      ]
    }

    principals {
      type        = "*"
      identifiers = ["*"]
    }
  }

  statement {
    sid       = "Allow use of the key"
    effect    = "Allow"
    resources = ["*"]

    actions = [
      "kms:Encrypt",
      "kms:Decrypt",
      "kms:ReEncrypt*",
      "kms:GenerateDataKey*",
      "kms:DescribeKey",
    ]

    principals {
      type = "AWS"

      identifiers = [
        "${data.aws_iam_role.federated_user.arn}",
        "${aws_iam_role.this.arn}"
      ]
    }
  }

  statement {
    sid       = "Allow attachment of persistent resources"
    effect    = "Allow"
    resources = ["*"]

    actions = [
      "kms:CreateGrant",
      "kms:ListGrants",
      "kms:RevokeGrant",
    ]

    condition {
      test     = "Bool"
      variable = "kms:GrantIsForAWSResource"
      values   = ["true"]
    }

    principals {
      type = "AWS"

      identifiers = [
        "${data.aws_iam_role.federated_user.arn}",
        "${aws_iam_role.this.arn}"
      ]
    }
  }

  statement {
    sid       = "Allow Cloudwatch Log Group"
    effect    = "Allow"
    resources = ["*"]

    actions = [
      "kms:Encrypt*",
      "kms:Decrypt*",
      "kms:ReEncrypt*",
      "kms:GenerateDataKey*",
      "kms:Describe*",
    ]

    condition {
      test     = "ArnEquals"
      variable = "kms:EncryptionContext:aws:logs:arn"
      values   = ["arn:${data.aws_partition.current.id}:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/aws/eks/${var.cluster_name}/cluster"]
    }

    principals {
      type        = "Service"
      identifiers = ["logs.${data.aws_region.current.name}.amazonaws.com"]
    }
  }
}