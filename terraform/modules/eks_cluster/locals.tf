locals {
  ba_map = {
    "Enterprise Operations"      = "eo"
    "Aeronautics Company"        = "aero"
    "Missiles and Fire Control"  = "mfc"
    "Rotary and Mission Systems" = "rms"
    "Space"                      = "space"
  }
  common_tags = {
    CreatedDate         = timestamp()
    ManagedBy           = "Terraform"
    ClusterName         = var.cluster_name
    lmcommonid          = var.common_id
    lmsystemenvironment = var.environment
    lmbusinessarea      = var.business_area
  }
  eks_cluster_access_policy_arns = {
    cluster_admin = "arn:aws:eks::aws:cluster-access-policy/AmazonEKSClusterAdminPolicy"
    admin         = "arn:aws:eks::aws:cluster-access-policy/AmazonEKSAdminPolicy"
    view          = "arn:aws:eks::aws:cluster-access-policy/AmazonEKSViewPolicy"
    edit          = "arn:aws:eks::aws:cluster-access-policy/AmazonEKSEditPolicy"
  }
  required_lmco_tags = {
    lmsystemenvironment = var.environment
    lmbusinessarea      = lower(local.ba_map[var.business_area])
    lmcommonid          = var.common_id
    lmdrcriticality     = var.dr_criticality
    lmoptout            = var.optout
  }
}