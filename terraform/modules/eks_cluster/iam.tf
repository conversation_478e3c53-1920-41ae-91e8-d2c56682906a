resource "aws_iam_role" "this" {
  name               = "eks-cluster-${var.cluster_name}"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json
  tags = merge(
    var.resource_tags,
    local.common_tags,
    {
      Name        = "eks-cluster-iam-role"
      Description = "iam role for the eks cluster"
    },
  )

  lifecycle {
    ignore_changes = [
      tags["CreatedDate"]
    ]
  }
}

resource "aws_iam_role_policy_attachment" "common_deny" {
  policy_arn = data.aws_iam_policy.common_deny.arn
  role       = aws_iam_role.this.name
}

resource "aws_iam_role_policy_attachment" "amazon_eks_cluster_policy" {
  policy_arn = data.aws_iam_policy.amazon_eks_cluster_policy.arn
  role       = aws_iam_role.this.name
}