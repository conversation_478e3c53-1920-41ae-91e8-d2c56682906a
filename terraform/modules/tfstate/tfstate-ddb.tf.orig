#############################################################################################
#                                                                                           #
# Title:        tfstate-ddb.tf                                                              #
# Version:                                                                                  #
#               2021-08-18 WRC. Make sure that the credentials are created prior to table   #
#               2020-01-19 WRC. Update settings for better protections                      #
#               2020-12-04 WRC. Initial                                                     #
# Create Date:  2020-12-04                                                                  #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                          #
# Description:                                                                              #
#               Established the terraform state DynamoDB fir this account                   #
#                                                                                           #
#############################################################################################

#-------------------------------------------------------------------------------------------#
# Resource Section                                                                          #
#-------------------------------------------------------------------------------------------#

#-------------------------------#
# DynamoDB State Table          #
#-------------------------------#
resource "aws_dynamodb_table" "tfstate" {
  depends_on        = [ aws_iam_access_key.tfstate, aws_iam_access_key.aws ]
  name              = format("%s-tfstate", var.common_data.prefix)
  billing_mode      = "PROVISIONED"
  hash_key          = "LockID"
  read_capacity     = 1
  write_capacity    = 1

  #-------------------------#
  # Lifecycle attributes    #
  #-------------------------#
  lifecycle {
    prevent_destroy = true
  }    

  #-------------------------#
  # Table attributes        #
  #-------------------------#
  attribute {
    name = "LockID"
    type = "S"
  }
  
  #-------------------------#
  # Recovery                #
  #-------------------------#
  point_in_time_recovery { 
    enabled = true 
  }
  
  #-------------------------#
  # Encryption              #
  #-------------------------#
  server_side_encryption {
    enabled     = true
    kms_key_arn = aws_kms_key.cmk["ddb"].arn   # Key may be changed without destroy
  }

  tags = merge({Name = "${var.common_data.prefix}-tfstate"}, var.common_data.tags)
}