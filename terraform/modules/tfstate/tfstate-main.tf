# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        tfstate-main.tf                                                   #
# Version:                                                                        #
#               2024-01-25 WRC. Integrated into Galaxy                            #
#               2021-08-09 WRC. Comment out providers as not needed in latest     #
#               2021-01-21 WRC. Add aws providers for replicated region           #
#               2020-12-07 WRC. Add outputs                                       #
#               2020-11-10 WRC. Converge variables and outputs in this file.      #
#               2020-06-09 WRC. Initial                                           #
# Create Date:  2020-06-09                                                        #
# Author:       <PERSON><PERSON><PERSON>, Wayne (US) <<EMAIL>>                #
# Description:                                                                    #
#               Established the terraform state this account and environment      #
#                                                                                 #
# ------------------------------------------------------------------------------- #

#-----------------------------------------------#
# Address missing reference to undefed provider #
#-----------------------------------------------#
terraform {
  required_providers { aws = {} }
}

#-----------------------------------------------#
# Variables Section                             #
#-----------------------------------------------#
variable "common_data" {}

#-----------------------------------------------#
# Outputs Section                               #
#-----------------------------------------------#
# output "tfstate_data" {
#   description = "The set of output data to make available to modules."
#   sensitive   = false
#   value = {
    #aws_kms_key_cmk             = aws_kms_key.cmk
    #aws_kms_alias_cmk           = aws_kms_alias.cmk
    #aws_kms_key_cmk_replica     = aws_kms_key.cmk-replica
    #aws_kms_alias_cmk_replica   = aws_kms_alias.cmk-replica
 #  }
# }
