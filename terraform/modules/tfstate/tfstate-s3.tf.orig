#############################################################################################
#                                                                                           #
# Title:        tfstate-s3.tf                                                               #
# Version:                                                                                  #
#               2021-08-18 WRC. Make sure that the credentials are created prior to bucket  #
#               2021-08-09 WRC. Map the S3 bucket policy for public and govcloud            #
#               2021-02-18 WRC. Refine bucket policy to include common-secrets.sh access    #
#               2021-02-16 WRC. Add bucket policy to get credentials                        #
#               2020-01-19 WRC. Update settings for better protections                      #
#               2020-12-04 WRC. Initial                                                     #
# Create Date:  2020-12-04                                                                  #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                          #
# Description:                                                                              #
#               Established the terraform state S3 bucket this account                      #
#                                                                                           #
# 2021-02-18 Note that AWS is going to check the principals when they are in the bucket     #
# policy so you have to make sure that they exist.  This means that the accounts in         #
# common/common-maps must exist or the updates will hang.                                   #
#                                                                                           #
# 2021-08-09 Note https://docs.aws.amazon.com/govcloud-us/latest/UserGuide/govcloud-iam.html#
# states "You cannot create a role to delegate access between an AWS GovCloud (US) account  #
# and a standard AWS account."  So temporarily set the S3 bucket to reference the local     #
# account rather than S3. Will need to create GIT user for public accounts.                 #
#                                                                                           #
#############################################################################################

#-------------------------------------------------------------------------------------------#
# Resource Section                                                                          #
#-------------------------------------------------------------------------------------------#

#-----------------------------------#
# This bucket policy was added to   #
# allow git access to get the       #
# credentials file.                 #
#-----------------------------------#
resource "aws_s3_bucket_policy" "tfstate" {
  bucket = aws_s3_bucket.tfstate.id
  policy = jsonencode({
    Version = "2012-10-17"
    Id      = "AllowAccessToSensitiveFiles"
    Statement = [
      {
        Sid       = "AllowGitAccessToCredentials"
        Effect    = "Allow"
        Action    = "s3:GetObject*"
        Principal = {
          AWS = "arn:${var.common_data.git_partition}:iam::${var.common_data.git_account}:user/${var.common_data.git_region_id}-global-${var.common_data.git_user}"
        }
        Resource  = [ 
          "${aws_s3_bucket.tfstate.arn}/credentials/credentials.${var.account}.${var.common_data.region_id}"
        ]
      }
#      {
#        Sid       = "AllowAccountAccessToCommon"
#        Effect    = "Allow"
#        Action    = "s3:GetObject"
#        Principal = {
#          AWS = ["************"] # ["************","************","************","************","************","************","************","************","************"] #  "************",#"************", ["************"]var.common_data.accounts #
#        } 
#        Resource  = [ "${aws_s3_bucket.tfstate.arn}/global/common-secrets.sh" ]
#      }
    ]
  })
}

#-----------------------------------#
# This bucket contains the tfstate  #
# data and the credentials for the  #
# account-region.                   #
#-----------------------------------#
resource "aws_s3_bucket" "tfstate" {
  depends_on        = [ aws_iam_access_key.tfstate, aws_iam_access_key.aws ]
  bucket            = format("%s-tfstate", var.common_data.prefix_global)
  acl               = "private"
  # region          = var.region
  force_destroy     = false
  
  #-------------------------#
  # Lifecycle attributes    #
  #-------------------------#
  lifecycle {
    prevent_destroy = true
  }
  
  #-------------------------#
  # Version attributes      #
  #-------------------------#
  versioning { 
    enabled         = true
    mfa_delete      = false
  }
  
  #-------------------------#
  # Encryption attributes   #
  #-------------------------#
  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        kms_master_key_id = aws_kms_key.cmk["s3"].arn     # var.common_data.LM_S3_CMK.arn
        sse_algorithm     = "aws:kms"
      }
    }
  }
  tags = merge({Name ="${var.common_data.prefix_global}-tfstate"}, var.common_data.tags)
}
