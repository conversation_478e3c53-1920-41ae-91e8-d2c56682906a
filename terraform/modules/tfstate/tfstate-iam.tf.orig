#############################################################################################
#                                                                                           #
# Title:        tfstate-iam.tf                                                              #
# Version:                                                                                  #
#               2021-08-18 WRC. Add tag resource on DDB table to allow changes to tags      #
#               2021-03-12 WRC. Update GIT user policy that allows access to other accts    #
#               2021-02-25 WRC. Put double quotes around the AWS keys due to char issues    #
#               2021-02-23 WRC. Include * in StringLike terraform.tfstate to support envs   #
#                               Add describe functions for ELB.                             #
#               2021-02-18 WRC. Limit DynamoDB Item access.                                 #
#               2021-02-17 WRC. Add second GIT key to support key rotation automatically    #
#               2021-02-15 WRC. Modifications to tfstate and aws to limit permissionst      #
#                               Create GIT credentials if the correct account.              #
#               2021-01-07 WRC. Add lambda                                                  #
#               2020-12-04 WRC. Move to tfstate since needed at initialization              #
#               2020-11-10 WR<PERSON>. Update the filename                                         #
#               2020-10-14 WRC. Update the AWS policy to support CloudFormation             #
#               2020-09-22 WRC. Update the AWS policy to support SNS and cloudwatch         #
#               2020-08-06 WRC. Add additional comments.  Jeff's SSM policy update also     #
#               2020-07-29 WRC. Updates put quotes around credentials due to special chars  #
#               2020-06-23 WRC. Updates to profiles to limit permissions.                   #
#               2020-06-09 WRC. Initial                                                     #
# Create Date:  2020-06-09                                                                  #
# <AUTHOR> <EMAIL>                          #
# Description:                                                                              #
#               Updates the configuration of the exisitng VPC provided as part of the       #
#               provisioning process.                                                       #
#                                                                                           #
#############################################################################################

#-------------------------------------------------------------------------------------------#
# Data Section                                                                              #
#-------------------------------------------------------------------------------------------#
data "aws_partition" "current" {}

#-------------------------------------------------------------------------------------------#
# Title:        git access section                                                          #
# Create Date:  2021-02-15                                                                  #
# Description:  These resources create a user and an associated access key for GIT          #
#               access to download the credentials.                                         #
#-------------------------------------------------------------------------------------------#

#-----------------------------------#
# GIT policy document               #
#-----------------------------------#
data "aws_iam_policy_document" "git" {
  
  #-----------------------------#
  # Access to All S3 Buckets    #
  #-----------------------------#
  statement {
    sid     = "AllowS3AccessAllAccounts"
    effect  = "Allow"
    actions = [
      "s3:GetObject*"
    ]
    resources = [ "*" ]
  }
  
  #-----------------------------#
  # Access to All KMS Keys      #
  #-----------------------------#
  statement {
    sid     = "AllowKMSKeyAccessAllAccounts"
    effect  = "Allow"
    actions     = [ 
      "kms:Decrypt",
      "kms:GenerateDataKey"
    ]
    resources   = ["*"]
  }
}  
  
#-----------------------------------#
# GIT User                          #
#-----------------------------------#
resource "aws_iam_user" "git" {
  count         = var.account == var.common_data.git_account ? 1 : 0    # if we are the account where the user is
  name          = format("%s-%s", var.common_data.prefix, var.common_data.git_user)
  path          = "/"
  force_destroy = true
  tags = merge( { Name = format("%s-%s", var.common_data.prefix, var.common_data.git_user) }, var.common_data.tags)
}

#-----------------------------------#
# GIT User Policy                   #
#-----------------------------------#
resource "aws_iam_user_policy" "git" {
  count         = var.account == var.common_data.git_account ? 1 : 0
  name          = format("%s-%s", var.common_data.prefix, var.common_data.git_user)
  user          = aws_iam_user.git[count.index].name
  policy        = data.aws_iam_policy_document.git.json
}

#-----------------------------------#
# GIT Access Keys.  There are 2     #
# available to support key rotation #
#-----------------------------------#
resource "aws_iam_access_key" "git" {
  count         = var.account == var.common_data.git_account ? 2 : 0
  user          = aws_iam_user.git[0].name
}

#-----------------------------------#
# GIT credentials.  Provides a      #
# simple way to lookup as needed.   #
#-----------------------------------#
resource "aws_s3_bucket_object" "credentials-git" {
  count         = var.account == var.common_data.git_account ? 2 : 0
  lifecycle { ignore_changes = [ cache_control ] }
  bucket                    = aws_s3_bucket.tfstate.id
  key                       = format("credentials/credentials.git.%02d", count.index)
  content_type              = "text/plain"
  acl                       = "private"
  force_destroy             = false
  server_side_encryption    = "AES256"
  storage_class             = "STANDARD"
  content = <<-EOF
    [git]
    aws_access_key_id = "${aws_iam_access_key.git[count.index].id}"
    aws_secret_access_key = "${aws_iam_access_key.git[count.index].secret}"
  EOF
}

#resource "random_integer" "active" {
#  count         = var.account == var.common_data.git_account ? 0 : 0
#  min     = 0
#  max     = 1
#  keepers = {
#    git00 = aws_s3_bucket_object.credentials-git[0].etag
#    git01 = aws_s3_bucket_object.credentials-git[1].etag
#  }
#}
#resource "aws_s3_bucket_object" "credentials-active" {
#  count         = var.account == var.common_data.git_account ? 0 : 0
#  lifecycle { ignore_changes = [ cache_control ] }
#  bucket                    = aws_s3_bucket.tfstate.id
#  key                       = "credentials/credentials.git.active"
 # content_type              = "text/plain"
#  acl                       = "private"
#  force_destroy             = false
#  server_side_encryption    = "AES256"
#  storage_class             = "STANDARD"
#  content = random_integer.active[0].result
#}

#-------------------------------------------------------------------------------------------#
# Title:        tfstate access section                                                      #
# Create Date:  2021-02-15                                                                  #
# Description:  These resources create a policy for each possible environment that may      #
#               be assigned to the credentials that are created.  It also includes the      #
#               user and credentials creation.                                              #
#-------------------------------------------------------------------------------------------#

#-----------------------------------#
# TFSTATE policy document           #
#-----------------------------------#
data "aws_iam_policy_document" "tfstate" {
  for_each  = var.common_data.environments

  #-----------------------------#
  # Access to DynamoDB item     #
  #-----------------------------#
  statement {
    sid         = "AllowDynamoDBAccess"
    effect      = "Allow"
    actions     = [
      "dynamodb:GetItem",
      "dynamodb:PutItem",
      "dynamodb:DeleteItem",
      "dynamodb:TagResource"
    ]
    resources   = [
      aws_dynamodb_table.tfstate.arn
    ]
    
    #-----------------------------------------------#
    # Note that the item key varies between         #
    # terraform.tfstate and terraform.tfstate-md5   #
    # so have to use the StringLike                 #
    #-----------------------------------------------#
    condition {
      test     = "ForAllValues:StringLike"
      variable = "dynamodb:LeadingKeys"
      values = [ format("%s/%s/%s/*terraform.tfstate*", aws_s3_bucket.tfstate.id, var.common_data.region_id, each.key) ]
    }    
  }
  
  #-----------------------------#
  # Access to Bucket Object     #
  #-----------------------------#
  statement {
    sid     = "AllowS3BucketAccess"
    effect  = "Allow"
    actions = [
      "s3:ListBucket",
      "s3:GetObject",
      "s3:PutObject"
    ]
    resources = [ 
      format("%s", aws_s3_bucket.tfstate.arn),
      format("%s/%s/%s/*", aws_s3_bucket.tfstate.arn, var.common_data.region_id, each.key)
    ] 
  }
  
  #-----------------------------#
  # Access to KMS Keys          #
  #-----------------------------#
  statement {
    sid     = "AllowKMSKeyAccess"
    effect  = "Allow"
    actions = [
      "kms:Decrypt",
      "kms:GenerateDataKey"
    ]
    resources = [ 
        aws_kms_alias.cmk["s3"].target_key_arn,
        aws_kms_alias.cmk["ddb"].target_key_arn
    ]
  }
}

#-----------------------------------#
# TFSTATE User                      #
#-----------------------------------#
resource "aws_iam_user" "tfstate" {
  for_each      = var.common_data.environments
  name          = format("%s-tfstate-%s", var.common_data.prefix, each.key)
  path          = "/"
  force_destroy = true
  tags = merge( { Name = format("%s-tfstate-%s", var.common_data.prefix, each.key) }, var.common_data.tags )
}

#-----------------------------------#
# TFSTATE User Policy               #
#-----------------------------------#
resource "aws_iam_user_policy" "tfstate" {
  for_each      = var.common_data.environments
  name          = format("%s-tfstate-%s", var.common_data.prefix, each.key)
  user          = aws_iam_user.tfstate[each.key].name
  policy        = data.aws_iam_policy_document.tfstate[each.key].json
}

#-----------------------------------#
# TFSTATE Access Key                #
#-----------------------------------#
resource "aws_iam_access_key" "tfstate" {
  for_each      = var.common_data.environments
  user          = aws_iam_user.tfstate[each.key].name
}

#-------------------------------------------------------------------------------------------#
# Title:        AWS access section                                                          #
# Create Date:  2021-02-15                                                                  #
# Description:  These resources create a policy for each possible environment that may      #
#               be assigned to the credentials that are created.  It also includes the      #
#               user and credentials creation.                                              #
#-------------------------------------------------------------------------------------------#

#-----------------------------------#
# AWS policy document               #
#-----------------------------------#
data "aws_iam_policy_document" "aws" {
  for_each  = var.common_data.environments
  
  #-----------------------------#
  # Access to DynamoDB item     #
  #-----------------------------#
  statement {
    sid     = "AllowDynamoDBAccess"
    effect  = "Allow"
    actions = [
      "dynamodb:GetItem",
      "dynamodb:PutItem",
      "dynamodb:DeleteItem",
      "dynamodb:DescribeTable",
      "dynamodb:DescribeTimeToLive",
      "dynamodb:ListTagsOfResource",
      "dynamodb:DescribeContinuousBackups",
      "dynamodb:TagResource"
    ]
    resources = [
      aws_dynamodb_table.tfstate.arn,
      format("%s/%s/%s/%s/*", aws_dynamodb_table.tfstate.arn, aws_s3_bucket.tfstate.id, var.common_data.region_id, each.key)
    ]
  }
  
  #-----------------------------#
  # Access to S3 Bucket         #
  #-----------------------------#
  statement {
    sid     = "AllowS3BucketAccess"
    effect  = "Allow"
    actions = [
      "s3:ListBucket",
      "s3:GetObject",
      "s3:PutObject",
      "s3:DeleteObject"
    ]
    resources = [
      aws_s3_bucket.tfstate.arn,
      "${aws_s3_bucket.tfstate.arn}/${var.common_data.region_id}/${var.environment}/*"
    ]
  }
  
  #-----------------------------#
  # ELB Resources               #
  #-----------------------------#
  statement {
    sid         = "AllowELBAccess"
    effect      = "Allow"
    actions     = [
      "elasticloadbalancing:*"
    ]
    resources = [
      #---------------------#
      # Environment LB's    #
      #---------------------#
      format("arn:%s:elasticloadbalancing:%s:%s:loadbalancer/net/%s-%s-*", 
        data.aws_partition.current.partition, 
        var.region, 
        var.account,
        var.common_data.region_id,
        each.key
      ),
      #---------------------#
      # Environment listener#
      #---------------------#
      format("arn:%s:elasticloadbalancing:%s:%s:listener/net/%s-%s-*/*/*", 
        data.aws_partition.current.partition, 
        var.region, 
        var.account,
        var.common_data.region_id,
        each.key
      ),
      #---------------------#
      # Environment TG's    #
      #---------------------#
      format("arn:%s:elasticloadbalancing:%s:%s:targetgroup/%s-%s-*/*", 
        data.aws_partition.current.partition, 
        var.region, 
        var.account,
        var.common_data.region_id,
        each.key
      )
    ]
  }
  statement {
    sid         = "AllowELBDescribeAnything"
    effect      = "Allow"
    actions     = [ "elasticloadbalancing:Describe*" ]
    resources = [ "*" ]
  }
  
  #-----------------------------#
  # Catch All - Needs refinement#
  #-----------------------------#
  statement {
    sid     = "AllowAllOtherStuff"
    effect  = "Allow"
    actions = [
      "autoscaling:*",
      "cloudformation:*",
      "cloudtrail:*",
      "cloudwatch:*",
      "ec2:*",
      "events:*",
      "iam:*",
      "kms:*",
      "lambda:*",             
      "logs:*",
      "rds:*",
      "s3:*",
      "ssm:*",
      "sns:*",
      "sts:*",
      "swf:*"
    ]
    resources = [ "*" ]
  }    
}

#-----------------------------------#
# AWS User                          #
#-----------------------------------#
resource "aws_iam_user" "aws" {
  for_each      = var.common_data.environments
  name          = format("%s-aws-%s", var.common_data.prefix, each.key)
  path          = "/"
  force_destroy = true
  tags = merge( { Name = format("%s-aws-%s", var.common_data.prefix, each.key) }, var.common_data.tags )
}

#-----------------------------------#
# AWS User Policy                   #
#-----------------------------------#
resource "aws_iam_user_policy" "aws" {
  for_each      = var.common_data.environments
  name          = format("%s-aws-%s", var.common_data.prefix, each.key)
  user          = aws_iam_user.aws[each.key].name
  policy        = data.aws_iam_policy_document.aws[each.key].json
}

#-----------------------------------#
# AWS Access Key                    #
#-----------------------------------#
resource "aws_iam_access_key" "aws" {
  for_each      = var.common_data.environments
  user          = aws_iam_user.aws[each.key].name
}

#-------------------------------------------------------------------------------------------#
# Title:        credentials                                                                 #
# Create Date:  2020-06-09                                                                  #
# Description:  These resources create a local credentials file that terraform uses for     #
#               access.  Since this process is a bit circular in that TF needs to run to    #
#               actually generate these, we kick start the process with a separate          #
#               credentials file with full access.  This file needs to be part of the git   #
#               repo once the region is initialized.                                        #
#-------------------------------------------------------------------------------------------#

#-----------------------------------#
# The local file is included for    #
# ease of use to create the creds   #
# in local user account.            #
#-----------------------------------#
#resource "local_file" "credentials" {
#    lifecycle {
#      create_before_destroy = true
#    }
#    content     = aws_s3_bucket_object.credentials.content
#    file_permission = "0640"
#    filename = format(pathexpand("~/.aws/credentials.%s.%s"), var.account, var.common_data.region_id)
#}
resource "aws_s3_bucket_object" "credentials" {
  lifecycle { ignore_changes = [ cache_control ] }
  bucket                    = aws_s3_bucket.tfstate.id
  key                       = format("credentials/credentials.%s.%s", var.account, var.common_data.region_id)
  content_type              = "text/plain"
  acl                       = "private"
  force_destroy             = false
  server_side_encryption    = "AES256"
  storage_class             = "STANDARD"
  content = join ( "\n", concat(
                [ 
                  for key,value in var.common_data.environments:
                    join ("\n", [
                        format( "[tfstate.%s]", key),
                        "aws_access_key_id=${aws_iam_access_key.tfstate[key].id}",
                        "aws_secret_access_key=${aws_iam_access_key.tfstate[key].secret}",
                        "region=${var.region}",
                        "output=json",
                        "max_attempts=50",
                        format( "[aws.%s]", key),
                        "aws_access_key_id=${aws_iam_access_key.aws[key].id}",
                        "aws_secret_access_key=${aws_iam_access_key.aws[key].secret}",
                        "region=${var.region}",
                        "output=json",
                        "max_attempts = 50"
                    ])
                ]
            ))
}

