#############################################################################################
#                                                                                           #
# Title:        outputs.tf                                                                  #
# Version:                                                                                  #
#               2024-10-05 Initial creation for VPC module                                  #
# Create Date:  2024-10-05                                                                  #
# Description:                                                                              #
#               Output definitions for the VPC Terraform module                            #
#                                                                                           #
#############################################################################################

#-------------------------------------------------------------------------------------------#
# VPC Outputs                                                                               #
#-------------------------------------------------------------------------------------------#
output "vpc_id" {
  description = "ID of the VPC"
  value       = var.vpc_init ? aws_vpc.main[0].id : null
}

output "vpc_arn" {
  description = "ARN of the VPC"
  value       = var.vpc_init ? aws_vpc.main[0].arn : null
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = var.vpc_init ? aws_vpc.main[0].cidr_block : null
}

output "vpc_default_security_group_id" {
  description = "ID of the default security group"
  value       = var.vpc_init ? aws_vpc.main[0].default_security_group_id : null
}

output "vpc_default_network_acl_id" {
  description = "ID of the default network ACL"
  value       = var.vpc_init ? aws_vpc.main[0].default_network_acl_id : null
}

output "vpc_default_route_table_id" {
  description = "ID of the default route table"
  value       = var.vpc_init ? aws_vpc.main[0].default_route_table_id : null
}

#-------------------------------------------------------------------------------------------#
# Subnet Outputs                                                                            #
#-------------------------------------------------------------------------------------------#
output "public_subnet_id" {
  description = "ID of the public subnet"
  value       = var.vpc_init ? aws_subnet.public[0].id : null
}

output "public_subnet_arn" {
  description = "ARN of the public subnet"
  value       = var.vpc_init ? aws_subnet.public[0].arn : null
}

output "public_subnet_cidr_block" {
  description = "CIDR block of the public subnet"
  value       = var.vpc_init ? aws_subnet.public[0].cidr_block : null
}

output "public_subnet_availability_zone" {
  description = "Availability zone of the public subnet"
  value       = var.vpc_init ? aws_subnet.public[0].availability_zone : null
}

#-------------------------------------------------------------------------------------------#
# Internet Gateway Outputs                                                                  #
#-------------------------------------------------------------------------------------------#
output "internet_gateway_id" {
  description = "ID of the Internet Gateway"
  value       = var.vpc_init ? aws_internet_gateway.igw[0].id : null
}

output "internet_gateway_arn" {
  description = "ARN of the Internet Gateway"
  value       = var.vpc_init ? aws_internet_gateway.igw[0].arn : null
}

#-------------------------------------------------------------------------------------------#
# Route Table Outputs                                                                       #
#-------------------------------------------------------------------------------------------#
output "public_route_table_id" {
  description = "ID of the public route table"
  value       = var.vpc_init ? aws_route_table.public[0].id : null
}

output "public_route_table_association_id" {
  description = "ID of the route table association"
  value       = var.vpc_init ? aws_route_table_association.public_assoc[0].id : null
}

#-------------------------------------------------------------------------------------------#
# Availability Zone Outputs                                                                 #
#-------------------------------------------------------------------------------------------#
output "available_availability_zones" {
  description = "List of available availability zones"
  value       = data.aws_availability_zones.available.names
}

#-------------------------------------------------------------------------------------------#
# Composite Output for Backward Compatibility                                               #
#-------------------------------------------------------------------------------------------#
output "vpc_data" {
  description = "The set of output data to make available to modules (backward compatibility)"
  sensitive   = false
  value = var.vpc_init ? {
    vpc_id                = aws_vpc.main[0].id
    vpc_arn               = aws_vpc.main[0].arn
    vpc_cidr_block        = aws_vpc.main[0].cidr_block
    public_subnet_id      = aws_subnet.public[0].id
    public_subnet_arn     = aws_subnet.public[0].arn
    public_subnet_cidr    = aws_subnet.public[0].cidr_block
    internet_gateway_id   = aws_internet_gateway.igw[0].id
    internet_gateway_arn  = aws_internet_gateway.igw[0].arn
    public_route_table_id = aws_route_table.public[0].id
    availability_zone     = aws_subnet.public[0].availability_zone
    available_azs         = data.aws_availability_zones.available.names
  } : {}
}
