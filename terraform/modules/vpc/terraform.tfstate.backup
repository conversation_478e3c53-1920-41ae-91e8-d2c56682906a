{"version": 4, "terraform_version": "1.7.0", "serial": 11, "lineage": "33f74af7-69ab-8f0c-976c-57b0ffc53847", "outputs": {}, "resources": [{"mode": "data", "type": "aws_iam_policy_document", "name": "default", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Sid\": \"Enable IAM User Permissions\",\n      \"Effect\": \"Allow\",\n      \"Action\": \"kms:*\",\n      \"Resource\": \"*\",\n      \"Principal\": {\n        \"AWS\": \"*\"\n      }\n    }\n  ]\n}", "override_policy_documents": null, "policy_id": null, "source_policy_documents": null, "statement": [{"actions": ["kms:*"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [{"identifiers": ["*"], "type": "AWS"}], "resources": ["*"], "sid": "Enable IAM User Permissions"}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_iam_policy_document", "name": "s3", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Sid\": \"Enable GIT KMS Permissions\",\n      \"Effect\": \"Allow\",\n      \"Action\": \"kms:*\",\n      \"Resource\": \"*\",\n      \"Principal\": {\n        \"AWS\": \"*\"\n      }\n    },\n    {\n      \"Sid\": \"Enable GIT Bucket Permissions\",\n      \"Effect\": \"Allow\",\n      \"Action\": \"s3:GotObject*\",\n      \"Resource\": \"*\",\n      \"Principal\": {\n        \"AWS\": \"*\"\n      }\n    }\n  ]\n}", "override_policy_documents": null, "policy_id": null, "source_policy_documents": null, "statement": [{"actions": ["kms:*"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [{"identifiers": ["*"], "type": "AWS"}], "resources": ["*"], "sid": "Enable GIT KMS Permissions"}, {"actions": ["s3:GotObject*"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [{"identifiers": ["*"], "type": "AWS"}], "resources": ["*"], "sid": "Enable GIT Bucket Permissions"}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_kms_alias", "name": "cmk", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "ddb", "schema_version": 0, "attributes": {"arn": "arn:aws:kms:us-east-1:732988479216:alias/galaxy-ddb", "id": "alias/galaxy-ddb", "name": "alias/galaxy-ddb", "name_prefix": "", "target_key_arn": "arn:aws:kms:us-east-1:732988479216:key/b2d146ea-5bee-4138-88cf-d6d85259e570", "target_key_id": "b2d146ea-5bee-4138-88cf-d6d85259e570"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_kms_key.cmk", "data.aws_iam_policy_document.default", "data.aws_iam_policy_document.s3"]}, {"index_key": "ebs", "schema_version": 0, "attributes": {"arn": "arn:aws:kms:us-east-1:732988479216:alias/galaxy-ebs", "id": "alias/galaxy-ebs", "name": "alias/galaxy-ebs", "name_prefix": "", "target_key_arn": "arn:aws:kms:us-east-1:732988479216:key/1d713d3e-3648-449b-8cce-15d65981d7de", "target_key_id": "1d713d3e-3648-449b-8cce-15d65981d7de"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_kms_key.cmk", "data.aws_iam_policy_document.default", "data.aws_iam_policy_document.s3"]}, {"index_key": "rds", "schema_version": 0, "attributes": {"arn": "arn:aws:kms:us-east-1:732988479216:alias/galaxy-rds", "id": "alias/galaxy-rds", "name": "alias/galaxy-rds", "name_prefix": "", "target_key_arn": "arn:aws:kms:us-east-1:732988479216:key/aeacaadb-8664-4e47-a16b-3eeb0f0b92ce", "target_key_id": "aeacaadb-8664-4e47-a16b-3eeb0f0b92ce"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_kms_key.cmk", "data.aws_iam_policy_document.default", "data.aws_iam_policy_document.s3"]}, {"index_key": "s3", "schema_version": 0, "attributes": {"arn": "arn:aws:kms:us-east-1:732988479216:alias/galaxy-s3", "id": "alias/galaxy-s3", "name": "alias/galaxy-s3", "name_prefix": "", "target_key_arn": "arn:aws:kms:us-east-1:732988479216:key/24d3c2b9-f22e-421c-af86-5f58befac178", "target_key_id": "24d3c2b9-f22e-421c-af86-5f58befac178"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_kms_key.cmk", "data.aws_iam_policy_document.default", "data.aws_iam_policy_document.s3"]}, {"index_key": "ssm", "schema_version": 0, "attributes": {"arn": "arn:aws:kms:us-east-1:732988479216:alias/galaxy-ssm", "id": "alias/galaxy-ssm", "name": "alias/galaxy-ssm", "name_prefix": "", "target_key_arn": "arn:aws:kms:us-east-1:732988479216:key/1177148b-aef0-4db1-9ca2-7f7c35b9891f", "target_key_id": "1177148b-aef0-4db1-9ca2-7f7c35b9891f"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_kms_key.cmk", "data.aws_iam_policy_document.default", "data.aws_iam_policy_document.s3"]}]}, {"mode": "managed", "type": "aws_kms_key", "name": "cmk", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "ddb", "schema_version": 0, "attributes": {"arn": "arn:aws:kms:us-east-1:732988479216:key/b2d146ea-5bee-4138-88cf-d6d85259e570", "bypass_policy_lockout_safety_check": false, "custom_key_store_id": "", "customer_master_key_spec": "SYMMETRIC_DEFAULT", "deletion_window_in_days": 7, "description": "Customer Managed Key for ddb", "enable_key_rotation": true, "id": "b2d146ea-5bee-4138-88cf-d6d85259e570", "is_enabled": true, "key_id": "b2d146ea-5bee-4138-88cf-d6d85259e570", "key_usage": "ENCRYPT_DECRYPT", "multi_region": false, "policy": "{\"Statement\":[{\"Action\":\"kms:*\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"*\"},\"Resource\":\"*\",\"Sid\":\"Enable IAM User Permissions\"}],\"Version\":\"2012-10-17\"}", "tags": null, "tags_all": {}, "timeouts": null, "xks_key_id": ""}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDB9fQ==", "dependencies": ["data.aws_iam_policy_document.default", "data.aws_iam_policy_document.s3"]}, {"index_key": "ebs", "schema_version": 0, "attributes": {"arn": "arn:aws:kms:us-east-1:732988479216:key/1d713d3e-3648-449b-8cce-15d65981d7de", "bypass_policy_lockout_safety_check": false, "custom_key_store_id": "", "customer_master_key_spec": "SYMMETRIC_DEFAULT", "deletion_window_in_days": 7, "description": "Customer Managed Key for ebs", "enable_key_rotation": true, "id": "1d713d3e-3648-449b-8cce-15d65981d7de", "is_enabled": true, "key_id": "1d713d3e-3648-449b-8cce-15d65981d7de", "key_usage": "ENCRYPT_DECRYPT", "multi_region": false, "policy": "{\"Statement\":[{\"Action\":\"kms:*\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"*\"},\"Resource\":\"*\",\"Sid\":\"Enable IAM User Permissions\"}],\"Version\":\"2012-10-17\"}", "tags": null, "tags_all": {}, "timeouts": null, "xks_key_id": ""}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDB9fQ==", "dependencies": ["data.aws_iam_policy_document.default", "data.aws_iam_policy_document.s3"]}, {"index_key": "rds", "schema_version": 0, "attributes": {"arn": "arn:aws:kms:us-east-1:732988479216:key/aeacaadb-8664-4e47-a16b-3eeb0f0b92ce", "bypass_policy_lockout_safety_check": false, "custom_key_store_id": "", "customer_master_key_spec": "SYMMETRIC_DEFAULT", "deletion_window_in_days": 7, "description": "Customer Managed Key for rds", "enable_key_rotation": true, "id": "aeacaadb-8664-4e47-a16b-3eeb0f0b92ce", "is_enabled": true, "key_id": "aeacaadb-8664-4e47-a16b-3eeb0f0b92ce", "key_usage": "ENCRYPT_DECRYPT", "multi_region": false, "policy": "{\"Statement\":[{\"Action\":\"kms:*\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"*\"},\"Resource\":\"*\",\"Sid\":\"Enable IAM User Permissions\"}],\"Version\":\"2012-10-17\"}", "tags": null, "tags_all": {}, "timeouts": null, "xks_key_id": ""}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDB9fQ==", "dependencies": ["data.aws_iam_policy_document.default", "data.aws_iam_policy_document.s3"]}, {"index_key": "s3", "schema_version": 0, "attributes": {"arn": "arn:aws:kms:us-east-1:732988479216:key/24d3c2b9-f22e-421c-af86-5f58befac178", "bypass_policy_lockout_safety_check": false, "custom_key_store_id": "", "customer_master_key_spec": "SYMMETRIC_DEFAULT", "deletion_window_in_days": 7, "description": "Customer Managed Key for s3", "enable_key_rotation": true, "id": "24d3c2b9-f22e-421c-af86-5f58befac178", "is_enabled": true, "key_id": "24d3c2b9-f22e-421c-af86-5f58befac178", "key_usage": "ENCRYPT_DECRYPT", "multi_region": false, "policy": "{\"Statement\":[{\"Action\":\"kms:*\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"*\"},\"Resource\":\"*\",\"Sid\":\"Enable GIT KMS Permissions\"},{\"Action\":\"s3:GotObject*\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"*\"},\"Resource\":\"*\",\"Sid\":\"Enable GIT Bucket Permissions\"}],\"Version\":\"2012-10-17\"}", "tags": null, "tags_all": {}, "timeouts": null, "xks_key_id": ""}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDB9fQ==", "dependencies": ["data.aws_iam_policy_document.default", "data.aws_iam_policy_document.s3"]}, {"index_key": "ssm", "schema_version": 0, "attributes": {"arn": "arn:aws:kms:us-east-1:732988479216:key/1177148b-aef0-4db1-9ca2-7f7c35b9891f", "bypass_policy_lockout_safety_check": false, "custom_key_store_id": "", "customer_master_key_spec": "SYMMETRIC_DEFAULT", "deletion_window_in_days": 7, "description": "Customer Managed Key for ssm", "enable_key_rotation": true, "id": "1177148b-aef0-4db1-9ca2-7f7c35b9891f", "is_enabled": true, "key_id": "1177148b-aef0-4db1-9ca2-7f7c35b9891f", "key_usage": "ENCRYPT_DECRYPT", "multi_region": false, "policy": "{\"Statement\":[{\"Action\":\"kms:*\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"*\"},\"Resource\":\"*\",\"Sid\":\"Enable IAM User Permissions\"}],\"Version\":\"2012-10-17\"}", "tags": null, "tags_all": {}, "timeouts": null, "xks_key_id": ""}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDB9fQ==", "dependencies": ["data.aws_iam_policy_document.default", "data.aws_iam_policy_document.s3"]}]}], "check_results": null}