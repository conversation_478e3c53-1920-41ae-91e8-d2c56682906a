#############################################################################################
#                                                                                           #
# Title:        vpc-cmk.tf                                                                  #
# Version:                                                                                  #
#               2021-08-10 WRC. Add support for public GIT user and creds                   #
#               2021-03-08 WRC. Change RDB to RDS key name                                  #
#               2021-01-20 WRC. Add replica keys                                            #
#               2020-12-02 WRC. Add SSM CMK                                                 #
#               2020-11-25 WRC. Include partition in default policy                         #
#               2020-11-10 WRC. Initial                                                     #
# Create Date:  2020-11-10                                                                  #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                          #
# Description:                                                                              #
#               This resousrce file creates all the Customer Managed Keys {CMK's) for the   #
#               VPC. I set this up to use a default policy document that is generated by    #
#               Terraform and used in the map.  In this way, we can adjust and create       #
#               unique policies if we need to.  The default policy will allow full control  #
#               within the VPC but that should be fine because T<PERSON> manages the resource.     #
#                                                                                           #
#               Note: Was going to retrofit the tfstate with these keys but chose to leave  #
#               these available for environment resources only.                             #
#                                                                                           #
#############################################################################################

#-------------------------------------------------------------------------------------------#
# Data Section                                                                              #
#-------------------------------------------------------------------------------------------#
data "aws_iam_policy_document" "default" {
  statement {
    sid         = "Enable IAM User Permissions"
    effect      = "Allow"
    actions     = ["kms:*"]
    resources   = ["*"]
    principals {
      type          = "AWS"
      # identifiers   = [ format("arn:%s:iam::%s:root", var.common_data.aws_partition, var.account) ]
      identifiers   = [ "*" ]

    }
  }
}

data "aws_iam_policy_document" "s3" {
  statement {
    sid         = "Enable GIT KMS Permissions"
    effect      = "Allow"
    actions     = ["kms:*"]
    resources   = ["*"]
    principals {
      type          = "AWS"
      identifiers   = [ "*" ]
        # format("arn:%s:iam::%s:root", var.common_data.aws_partition, var.account),
        # "arn:${var.common_data.git_partition}:iam::${var.common_data.git_account}:user/${var.common_data.git_region_id}-global-${var.common_data.git_user}"
    }
  }
  statement {
    sid         = "Enable GIT Bucket Permissions"
    effect      = "Allow"
    actions     = [
        "s3:GotObject*"
    ]
    resources   = ["*"]
    principals {
      type          = "AWS"
      identifiers   = [ "*" ]
      # identifiers   = [ "arn:${var.common_data.git_partition}:iam::${var.common_data.git_account}:user/${var.common_data.git_region_id}-global-${var.common_data.git_user}" ]
    }
  }
}

#-------------------------------------------------------------------------------------------#
# Locals Section                                                                            #
#-------------------------------------------------------------------------------------------#
locals {
  keys  = {
    ddb         = { policy = data.aws_iam_policy_document.default }
    ebs         = { policy = data.aws_iam_policy_document.default }
    rds         = { policy = data.aws_iam_policy_document.default }
    s3          = { policy = data.aws_iam_policy_document.s3 }
    ssm         = { policy = data.aws_iam_policy_document.default }
  }
  key-replicas  = {
    s3-replica  = { policy = data.aws_iam_policy_document.default }
  }
}

#-------------------------------------------------------------------------------------------#
# Resource Section - Region Buckets                                                         #
#-------------------------------------------------------------------------------------------#

#-----------------------------------------------#
# This resource creates a map/list of the keys  #
# that are used in this VPC.                    #
#-----------------------------------------------#
resource "aws_kms_key" "cmk" {
  for_each                  = local.keys
  description               = format("Customer Managed Key for %s", each.key)
  customer_master_key_spec  = "SYMMETRIC_DEFAULT"
  deletion_window_in_days   = 7
  enable_key_rotation       = true
  is_enabled                = true
  key_usage                 = "ENCRYPT_DECRYPT"
  policy                    = each.value.policy.json

  # tags = merge( { Name = format("%s-%s", var.common_data.prefix, each.key) }, var.common_data.tags )
}

#-----------------------------------------------#
# This resource creates a map/list of the key   #
# aliases that are used in this VPC.            #
#-----------------------------------------------#
resource "aws_kms_alias" "cmk" {
  for_each                  = local.keys
  name                      = format("alias/%s-%s", "galaxy", each.key)
  # name                      = format("alias/%s-%s", "var.common_data.prefix", each.key)
  target_key_id             = aws_kms_key.cmk[each.key].key_id
}

#-------------------------------------------------------------------------------------------#
# Resource Section - Replica Buckets                                                        #
#-------------------------------------------------------------------------------------------#

#-----------------------------------------------#
# This resource creates a map/list of the keys  #
# replicas that are used in this VPC.           #
#-----------------------------------------------#
# resource "aws_kms_key" "cmk-replica" {
#   for_each                  = local.key-replicas
#   provider                  = aws.replica
#   description               = format("Customer Managed Key Replica %s", each.key)
#   customer_master_key_spec  = "SYMMETRIC_DEFAULT"
#   deletion_window_in_days   = 7
#   enable_key_rotation       = true
#   is_enabled                = true
#   key_usage                 = "ENCRYPT_DECRYPT"
#   policy                    = each.value.policy.json

#   tags = merge( { Name = format("%s-%s", var.common_data.prefix, each.key) }, var.common_data.tags )
# }

#-----------------------------------------------#
# This resource creates a map/list of the key   #
# aliases that are used in this VPC.            #
#-----------------------------------------------#
# resource "aws_kms_alias" "cmk-replica" {
#   for_each                  = local.key-replicas
#   provider                  = aws.replica
#   name                      = format("alias/%s-%s", var.common_data.prefix, each.key)
#   target_key_id             = aws_kms_key.cmk-replica[each.key].key_id
# }
