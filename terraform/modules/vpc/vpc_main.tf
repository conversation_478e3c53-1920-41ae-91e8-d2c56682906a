#############################################################################################
#                                                                                           #
# Title:        vpc_main.tf                                                                 #
# Version:                                                                                  #
#               2024-10-05 Updated for VPC infrastructure deployment                        #
#               2021-11-09 WRC. Add provider def to eliminate warnings in latest version    #
#               2020-12-07 WRC. Add outputs                                                 #
#               2020-11-10 WRC. Converge variables and outputs in this file.                #
#               2020-06-09 WRC. Initial                                                     #
# Create Date:  2020-06-09                                                                  #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                          #
# Description:                                                                              #
#               VPC infrastructure including VPC, subnets, internet gateway, and routing   #
#                                                                                           #
#############################################################################################

# Variables are now defined in variables.tf

#-------------------------------------------------------------------------------------------#
# Data Section                                                                              #
#-------------------------------------------------------------------------------------------#
data "aws_availability_zones" "available" {
  state = "available"
}

#-------------------------------------------------------------------------------------------#
# VPC Resources                                                                             #
#-------------------------------------------------------------------------------------------#

# VPC
resource "aws_vpc" "main" {
  count                = var.vpc_init ? 1 : 0
  cidr_block           = var.vpc_cidr_block
  enable_dns_support   = var.enable_dns_support
  enable_dns_hostnames = var.enable_dns_hostnames

  tags = merge(
    {
      Name = format("%s-vpc", var.environment)
    },
    try(var.common_data.tags, {})
  )
}

# Public Subnet
resource "aws_subnet" "public" {
  count                   = var.vpc_init ? 1 : 0
  vpc_id                  = aws_vpc.main[0].id
  cidr_block              = var.public_subnet_cidr
  availability_zone       = var.availability_zone != "" ? var.availability_zone : data.aws_availability_zones.available.names[0]
  map_public_ip_on_launch = var.map_public_ip_on_launch

  tags = merge(
    {
      Name = format("%s-public-subnet", var.environment)
    },
    try(var.common_data.tags, {})
  )
}

# Internet Gateway
resource "aws_internet_gateway" "igw" {
  count  = var.vpc_init ? 1 : 0
  vpc_id = aws_vpc.main[0].id

  tags = merge(
    {
      Name = format("%s-igw", var.environment)
    },
    try(var.common_data.tags, {})
  )
}

# Route Table for public subnet
resource "aws_route_table" "public" {
  count  = var.vpc_init ? 1 : 0
  vpc_id = aws_vpc.main[0].id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.igw[0].id
  }

  tags = merge(
    {
      Name = format("%s-public-route-table", var.environment)
    },
    try(var.common_data.tags, {})
  )
}

# Associate subnet with route table
resource "aws_route_table_association" "public_assoc" {
  count          = var.vpc_init ? 1 : 0
  subnet_id      = aws_subnet.public[0].id
  route_table_id = aws_route_table.public[0].id
}

# Outputs are now defined in outputs.tf