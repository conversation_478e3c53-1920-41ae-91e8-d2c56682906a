terraform {

  #---------------------#
  # common versions     #
  #---------------------#
#   required_version  = ">= 0.14.7"
  required_providers {
    aws = {
      source        = "hashicorp/aws"
      version       = "~> 5.33"
    }
  }
}

provider "aws" {
#   alias                     = "us-east-1"
  region                    = "us-east-1"
  # profile                   = format ("aws.%s", local.environment)
  shared_credentials_files  = [ "/mnt/c/users/c1620/gitrepos/galaxy/ansible_collections/lmco/aws/roles/aws_common/tests/credentials" ]
#   shared_credentials_file   = pathexpand(format("%s.%s.%s", "~/.aws/credentials", local.account, local.region_id))
  allowed_account_ids       = [ ************ ]
  max_retries               = 50
  insecure                  = false
}
