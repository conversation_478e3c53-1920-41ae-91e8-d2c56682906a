#############################################################################################
#                                                                                           #
# Title:        node-alarms.tf                                                              #
# Version:                                                                                  #
#               2020-12-11 WRC. Transition to a module that can be shared.                  #
#               2020-12-04 WRC. Update to use SNS topics from cft in AWS stack.             #
#               2020-09-09 JEZ. Initial                                                     #
# Create Date:  2020-09-09                                                                  #
# Author:       <PERSON><PERSON><PERSON><PERSON><PERSON> , <PERSON> (US) <jeffrey.e.z<PERSON><PERSON><PERSON><PERSON>@lmco.com>                 #
# Description:  Creates CloudWatch Alarms for Worker and Master hosts                       #
# Status checks are performed every minute, returning a pass or a fail status. If all       #
# checks pass, the overall status of the instance is OK. If one or more checks fail, the    #
# overall status is impaired. Status checks are built into Amazon EC2, so they cannot be    #
# disabled or deleted.                                                                      #
#                                                                                           #
#############################################################################################

#-------------------------------------------------------------------------------------------#
# Resource Section                                                                          #
#-------------------------------------------------------------------------------------------#

#-------------------------------------------------------------------#
# CloudWatch Alarm - CPU Utilization                                #
#-------------------------------------------------------------------#
resource "aws_cloudwatch_metric_alarm" "CPUUtilization" {
  count                     = length(var.aws_instance_list)
  alarm_name                = format("%s-%s", var.aws_instance_list[count.index].tags.Name, "CPUUtilization")
  alarm_description         = format("%s-%s", var.aws_instance_list[count.index].tags.Name, "CPUUtilization")
  comparison_operator       = "GreaterThanOrEqualToThreshold"
  evaluation_periods        = "15"
  metric_name               = "CPUUtilization"
  namespace                 = "AWS/EC2"
  period                    = "60"
  statistic                 = "Average"
  threshold                 = "80"
  insufficient_data_actions = []
  actions_enabled           = "true"
  alarm_actions             = [ data.aws_cloudformation_stack.sns-topics.outputs.AdminTopic ]
  ok_actions                = [ data.aws_cloudformation_stack.sns-topics.outputs.AdminTopic ]
  dimensions = {
    InstanceId  = var.aws_instance_list[count.index].id
  }
  tags = merge(
    { Name      = format("%s-%s", var.aws_instance_list[count.index].tags.Name, "CPUUtilization") },
    { Index     = format("%02s", count.index) },
      var.common_data.tags
  )
}

#-------------------------------------------------------------------#
# CloudWatch Alarm - Instance Status Check Failed                   #
#-------------------------------------------------------------------#
resource "aws_cloudwatch_metric_alarm" "StatusCheckFailedInstance" {
  count                     = length(var.aws_instance_list)
  alarm_name                = format("%s-%s", var.aws_instance_list[count.index].tags.Name, "StatusCheckFailedInstance")
  alarm_description         = format("%s-%s", var.aws_instance_list[count.index].tags.Name, "StatusCheckFailedInstance")
  comparison_operator       = "GreaterThanThreshold"
  evaluation_periods        = "15"
  metric_name               = "StatusCheckFailed_Instance"
  namespace                 = "AWS/EC2"
  period                    = "60"
  statistic                 = "Maximum"
  threshold                 = "0"
  insufficient_data_actions = []
  actions_enabled           = "true"
  alarm_actions             = [ data.aws_cloudformation_stack.sns-topics.outputs.AdminTopic ]
  ok_actions                = [ data.aws_cloudformation_stack.sns-topics.outputs.AdminTopic ]
  dimensions = {
    InstanceId  = var.aws_instance_list[count.index].id
  }
  
  tags = merge(
    { Name      = format("%s-%s", var.aws_instance_list[count.index].tags.Name, "StatusCheckFailedInstance") },
    { Index     = format("%02s", count.index) },
      var.common_data.tags
  )
}

#-------------------------------------------------------------------#
# CloudWatch Alarm - System Status Check Failed                     #
#-------------------------------------------------------------------#
resource "aws_cloudwatch_metric_alarm" "StatusCheckFailedSystem" {
  count                     = length(var.aws_instance_list)
  alarm_name                = format("%s-%s", var.aws_instance_list[count.index].tags.Name, "StatusCheckFailedSystem")
  alarm_description         = format("%s-%s", var.aws_instance_list[count.index].tags.Name, "StatusCheckFailedSystem")
  comparison_operator       = "GreaterThanThreshold"
  evaluation_periods        = "15"
  metric_name               = "StatusCheckFailed_System"
  namespace                 = "AWS/EC2"
  period                    = "60"
  statistic                 = "Maximum"
  threshold                 = "0"
  insufficient_data_actions = []
  actions_enabled           = "true"
  alarm_actions             = [ data.aws_cloudformation_stack.sns-topics.outputs.AdminTopic ]
  ok_actions                = [ data.aws_cloudformation_stack.sns-topics.outputs.AdminTopic ]
  dimensions = {
    InstanceId  = var.aws_instance_list[count.index].id
  }
  tags = merge(
    { Name      = format("%s-%s", var.aws_instance_list[count.index].tags.Name, "StatusCheckFailedSystem") },
    { Index     = format("%02s", count.index) },
      var.common_data.tags
  )
}

#-------------------------------------------------------------------#
# CloudWatch Alarm - Network inbound greater then 1 Mbyte/Second    #
#-------------------------------------------------------------------#
resource "aws_cloudwatch_metric_alarm" "NetworkIn" {
  count                     = length(var.aws_instance_list)
  alarm_name                = format("%s-%s", var.aws_instance_list[count.index].tags.Name, "NetworkIn")
  alarm_description         = format("%s-%s", var.aws_instance_list[count.index].tags.Name, "NetworkIn")
  comparison_operator       = "GreaterThanThreshold"
  evaluation_periods        = "30"
  metric_name               = "NetworkIn"
  namespace                 = "AWS/EC2"
  period                    = "60"
  statistic                 = "Average"
  threshold                 = "60000000"
  insufficient_data_actions = []
  actions_enabled           = "true"
  alarm_actions             = [ data.aws_cloudformation_stack.sns-topics.outputs.AdminTopic ]
  ok_actions                = [ data.aws_cloudformation_stack.sns-topics.outputs.AdminTopic ]
  dimensions = {
    InstanceId  = var.aws_instance_list[count.index].id
  }
  tags = merge(
    { Name      = format("%s-%s", var.aws_instance_list[count.index].tags.Name, "NetworkIn") },
    { Index     = format("%02s", count.index) },
      var.common_data.tags
  )
}

#-------------------------------------------------------------------#
# CloudWatch Alarm - Network outbound greater then 1 Mbyte/Second   #
#-------------------------------------------------------------------#
resource "aws_cloudwatch_metric_alarm" "NetworkOut" {
  count                     = length(var.aws_instance_list)
  alarm_name                = format("%s-%s", var.aws_instance_list[count.index].tags.Name, "NetworkOut")
  alarm_description         = format("%s-%s", var.aws_instance_list[count.index].tags.Name, "NetworkOut")
  comparison_operator       = "GreaterThanThreshold"
  evaluation_periods        = "30"
  metric_name               = "NetworkOut"
  namespace                 = "AWS/EC2"
  period                    = "60"
  statistic                 = "Average"
  threshold                 = "60000000"
  insufficient_data_actions = []
  actions_enabled           = "true"
  alarm_actions             = [ data.aws_cloudformation_stack.sns-topics.outputs.AdminTopic ]
  ok_actions                = [ data.aws_cloudformation_stack.sns-topics.outputs.AdminTopic ]
  dimensions = {
    InstanceId  = var.aws_instance_list[count.index].id
  }
  
  tags = merge(
    { Name      = format("%s-%s", var.aws_instance_list[count.index].tags.Name, "NetworkOut") },
    { Index     = format("%02s", count.index) },
      var.common_data.tags
  )
}

#-------------------------------------------------------------------------------------------#
# CPU credit metrics                                                                        #
# The AWS/EC2 namespace includes the following CPU credit metrics for your burstable        #
# performance instances.                                                                    #
#-------------------------------------------------------------------------------------------#
resource "aws_cloudwatch_metric_alarm" "CPUCreditUsage" {
  count                     = length(var.aws_instance_list)
  alarm_name                = format("%s-%s", var.aws_instance_list[count.index].tags.Name, "CPUCreditUsage")
  alarm_description         = format("%s-%s", var.aws_instance_list[count.index].tags.Name, "CPUCreditUsage")
  comparison_operator       = "GreaterThanThreshold"
  evaluation_periods        = "15"
  metric_name               = "CPUCreditUsage"
  namespace                 = "AWS/EC2"
  period                    = "300"
  statistic                 = "Average"
  threshold                 = "2"
  insufficient_data_actions = []
  actions_enabled           = "true"
  alarm_actions             = [ data.aws_cloudformation_stack.sns-topics.outputs.AdminTopic ]
  ok_actions                = [ data.aws_cloudformation_stack.sns-topics.outputs.AdminTopic ]
  dimensions = {
    InstanceId  = var.aws_instance_list[count.index].id
  }
  
  tags = merge(
    { Name      = format("%s-%s", var.aws_instance_list[count.index].tags.Name, "CPUCreditUsage") },
    { Index     = format("%02s", count.index) },
      var.common_data.tags
  )
}

