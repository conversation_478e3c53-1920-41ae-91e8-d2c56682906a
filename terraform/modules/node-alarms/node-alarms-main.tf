#############################################################################################
#                                                                                           #
# Title:        node-alarms-main.tf                                                         #
# Version:                                                                                  #
#               2021-11-09 WRC. Add provider def to eliminate warnings in latest version    #
#               2020-12-11 WRC. Initial                                                     #
# Create Date:  2020-12-11                                                                  #
# Author:       Casa<PERSON>nde, <PERSON> (US) <<EMAIL>>                          #
# Description:                                                                              #
#               Variables for this account, region and environment.                         #
#                                                                                           #
#############################################################################################
terraform { 
  required_providers { aws = {} }
}

#-------------------------------------------#
# Core Environment Variables (from module)  #
#-------------------------------------------#
variable "common_data"          {}
variable "aws_instance_list"    {}

#-------------------------------------------------------------------------------------------#
# Data Section                                                                              #
#-------------------------------------------------------------------------------------------#
data "aws_cloudformation_stack" "sns-topics" {
  name = format("%s-sns-topics", var.common_data.prefix)
}
