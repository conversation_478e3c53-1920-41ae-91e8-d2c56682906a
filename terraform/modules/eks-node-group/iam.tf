resource "aws_iam_role" "this" {
  count = var.create ? 1 : 0

  name               = "${local.name_prefix}-eks-worker-node"
  assume_role_policy = data.aws_iam_policy_document.assume_role_policy.json

  tags = merge(
    var.resource_tags,
    local.common_tags,
    {
      Name        = "eks-iam-node-role"
      Description = "iam role for EKS node group"
    }
  )

  lifecycle {
    ignore_changes = [
      tags["CreatedDate"]
    ]
  }
}

resource "aws_iam_role_policy_attachment" "common_deny" {
  count = var.create ? 1 : 0

  policy_arn = data.aws_iam_policy.common_deny.arn
  role       = aws_iam_role.this[0].name
}

resource "aws_iam_role_policy_attachment" "amazon_eks_cluster_policy" {
  count = var.create ? 1 : 0

  policy_arn = data.aws_iam_policy.amazon_eks_cluster_policy.arn
  role       = aws_iam_role.this[0].name
}

resource "aws_iam_role_policy_attachment" "amazon_eks_worker_node_policy" {
  count = var.create ? 1 : 0

  policy_arn = data.aws_iam_policy.amazon_eks_worker_node_policy.arn
  role       = aws_iam_role.this[0].name
}

resource "aws_iam_role_policy_attachment" "amazon_eks_cni_policy" {
  count = var.create ? 1 : 0

  policy_arn = data.aws_iam_policy.amazon_eks_cni_policy.arn
  role       = aws_iam_role.this[0].name
}

resource "aws_iam_role_policy_attachment" "amazon_ec2_container_registry_readonly" {
  count = var.create ? 1 : 0

  policy_arn = data.aws_iam_policy.amazon_ec2_container_registry_readonly.arn
  role       = aws_iam_role.this[0].name
}

resource "aws_iam_role_policy_attachment" "amazon_ec2_read_only_access" {
  count = var.create ? 1 : 0

  policy_arn = data.aws_iam_policy.amazon_ec2_read_only_access.arn
  role       = aws_iam_role.this[0].name
}