resource "aws_eks_node_group" "this" {
  count = var.create ? 1 : 0

  cluster_name    = var.cluster_name
  node_group_name = local.node_group_name
  node_role_arn   = aws_iam_role.this[0].arn
  subnet_ids      = var.node_group_subnet_ids

  launch_template {
    id      = aws_launch_template.this[0].id
    version = aws_launch_template.this[0].default_version
  }

  scaling_config {
    desired_size = var.desired_size
    max_size     = var.max_size
    min_size     = var.min_size
  }

  tags = merge(
    var.resource_tags,
    local.required_lmco_tags,
    local.common_tags,
    {
      Name        = local.node_group_name
      Description = "Terraform Created EKS Node Group"
    }
  )

  lifecycle {
    ignore_changes = [
      tags["CreatedDate"]
    ]
  }

  # Ensure that IAM Role permissions are created before and deleted after EKS Node Group handling.
  # Otherwise, EKS will not be able to properly delete EC2 Instances and Elastic Network Interfaces.
  depends_on = [
    aws_iam_role_policy_attachment.amazon_eks_worker_node_policy,
    aws_iam_role_policy_attachment.amazon_eks_cni_policy,
    aws_iam_role_policy_attachment.amazon_ec2_read_only_access,
  ]


}
