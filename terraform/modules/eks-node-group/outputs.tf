################################################################################
# CLUSTER
################################################################################

output "aws_eks_node_group_name" {
  description = "Name of the aws node group for to the cluster"
  value       = try(aws_eks_node_group.this[0].node_group_name, null)
}


################################################################################
# IAM Role
################################################################################

output "aws_iam_role_name" {
  description = "IAM role name of the Node Groups for the EKS Cluster"
  value       = try(aws_iam_role.this[0].name, null)
}

output "aws_iam_role_arn" {
  description = "IAM role Amazon Resource Name (ARN) of the Node Groups for the EKS Cluster"
  value       = try(aws_iam_role.this[0].name, null)
}

output "aws_iam_role_policy_attachment_ec2_container_registry_readonly" {
  description = "EC2 Container Registry Readonly Policy definition, to be consumed as a resource in IAM.tf"
  value       = try(aws_iam_role_policy_attachment.amazon_ec2_container_registry_readonly, null)
}

output "aws_iam_role_policy_attachment_eks_worker_node_policy" {
  description = "EKS Worker Node Policy definition, to be consumed as a resource in IAM.tf"
  value       = try(aws_iam_role_policy_attachment.amazon_eks_worker_node_policy, null)
}

output "aws_iam_role_policy_attachment_eks_cni_policy" {
  description = "EKS CNI Policy definition, to be consumed as a resource in IAM.tf"
  value       = try(aws_iam_role_policy_attachment.amazon_eks_cni_policy, null)
}

output "aws_iam_role_policy_attachment_ec2_read_only_access" {
  description = "EC2 Read only access policy definition, to be consumed as a resource in IAM.tf"
  value       = try(aws_iam_role_policy_attachment.amazon_ec2_read_only_access, null)
}

output "aws_iam_role_policy_attachment_amazon_common_deny" {
  description = "Required LMCO AWS Tag, added to code logic to avoid seeing changes that were expected, required in node creation process due to policy defintion restrictions"
  value       = try(aws_iam_role_policy_attachment.common_deny, null)
}

output "aws_iam_role_policy_attachment_amazon_eks_cluster_policy" {
  description = "EC2 Amazon EKS Cluster Policy definition, required in node creation process due to policy defintion restrictions"
  value       = try(aws_iam_role_policy_attachment.amazon_eks_cluster_policy, null)
}


################################################################################
# KMS Key
################################################################################

output "kms_key_id" {
  description = "ID of the KMS key of the Node Groups for the EKS Cluster"
  value       = try(aws_kms_key.this[0].id, null)
}

output "kms_key_arn" {
  description = "Amazon Resource Name (ARN) of the KMS key of the Node Groups for the EKS Cluster"
  value       = try(aws_kms_key.this[0].arn, null)
}

output "kms_key_alias" {
  description = "The KMS key alias of the Node Groups for the EKS Cluster"
  value       = try(aws_kms_alias.this, null)
}


################################################################################
# Launch Template
################################################################################

output "launch_templates" {
  description = "Name of the Node Groups Launch Template"
  value       = try(aws_launch_template.this[0].name, null)
}

output "launch_templates_id" {
  description = "ID of the Node Groups Launch Template"
  value       = try(aws_launch_template.this[0].id, null)
}

output "launch_templates_version" {
  description = "Version of the Node Groups Launch Template"
  value       = try(aws_launch_template.this[0].default_version, null)
}

