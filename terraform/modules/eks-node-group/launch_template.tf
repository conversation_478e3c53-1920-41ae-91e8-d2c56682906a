resource "aws_launch_template" "this" {
  count = var.create ? 1 : 0

  update_default_version = true
  name                   = "${local.name_prefix}-launch-template"
  user_data              = base64encode(templatefile("${path.module}/templates/linux_user_data.sh.tftpl", local.user_data_args))
  instance_type          = var.instance_type

  block_device_mappings {
    device_name = "/dev/xvda"
    ebs {
      encrypted   = true
      kms_key_id  = aws_kms_key.this[0].arn
      volume_size = var.volume_size
      volume_type = "gp3"
    }
  }

  tags = merge(
    var.resource_tags,
    local.required_lmco_tags,
    local.common_tags,
    {
      "eks:nodegroup-name" = local.node_group_name
      "eks:cluster-name"   = var.cluster_name
    }
  )

  tag_specifications {
    resource_type = "instance"

    tags = merge(
      var.resource_tags,
      {
        "eks:nodegroup-name" = local.node_group_name
        "eks:cluster-name"   = var.cluster_name
      },
    )
  }

  lifecycle {
    ignore_changes = [
      tags["CreatedDate"]
    ]
  }
}


