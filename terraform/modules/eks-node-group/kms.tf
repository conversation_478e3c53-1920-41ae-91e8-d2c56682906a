resource "aws_kms_key" "this" {
  count = var.create ? 1 : 0

  description             = "EKS NODE-GROUP KMS KEY ${var.cluster_name}"
  deletion_window_in_days = 30 # default value
  key_usage               = "ENCRYPT_DECRYPT"
  enable_key_rotation     = true
  tags                    = local.common_tags

  lifecycle {
    ignore_changes = [
      tags["CreatedDate"]
    ]
  }
}

resource "aws_kms_alias" "this" {
  count = var.create ? 1 : 0

  name_prefix   = "alias/eks-node-group-${var.node_group_name}-"
  target_key_id = aws_kms_key.this[0].arn
}

resource "aws_kms_key_policy" "this" {
  count = var.create ? 1 : 0

  key_id = aws_kms_key.this[0].id
  policy = data.aws_iam_policy_document.kms.json
}

data "aws_iam_policy_document" "kms" {
  statement {
    sid       = "Enable IAM User Permissions"
    effect    = "Allow"
    resources = ["*"]
    actions   = ["kms:*"]

    condition {
      test     = "StringLike"
      variable = "aws:PrincipalArn"
      values   = ["${data.aws_iam_role.federated_user.arn}"]
    }

    principals {
      type        = "*"
      identifiers = ["*"]
    }
  }

  statement {
    sid       = "Allow administration of the key"
    effect    = "Allow"
    resources = ["arn:${data.aws_partition.current.id}:kms:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:key/*"]

    actions = [
      "kms:CreateAlias",
      "kms:CreateGrant",
      "kms:CreateKey",
      "kms:DescribeKey",
      "kms:EnableKey",
      "kms:EnableKeyRotation",
      "kms:ListGrants",
      "kms:ListRetirableGrants",
      "kms:ListKeyPolicies",
      "kms:ListKeys",
      "kms:ListResourceTags",
      "kms:ListAliases",
      "kms:PutKeyPolicy",
      "kms:UpdateKeyDescription",
      "kms:UpdateAlias",
      "kms:RevokeGrant",
      "kms:RetireGrant",
      "kms:DisableKey",
      "kms:DisableKeyRotation",
      "kms:GetKeyPolicy",
      "kms:GetKeyRotationStatus",
      "kms:GetParametersForImport",
      "kms:DeleteAlias",
      "kms:DeleteImportedKeyMaterial",
      "kms:ImportKeyMaterial",
      "kms:TagResource",
      "kms:UntagResource",
      "kms:ScheduleKeyDeletion",
      "kms:CancelKeyDeletion",
      "kms:GenerateDataKeyWithoutPlaintext",
      "kms:GenerateRandom",
    ]

    condition {
      test     = "StringLike"
      variable = "aws:PrincipalArn"

      values = [
        "${data.aws_iam_role.federated_user.arn}",
        "${data.aws_caller_identity.current.arn}",
      ]
    }

    principals {
      type        = "*"
      identifiers = ["*"]
    }
  }

  statement {
    sid       = "Allow Autoscaling Service"
    effect    = "Allow"
    resources = ["*"]
    actions = [
      "kms:Encrypt",
      "kms:Decrypt",
      "kms:ReEncrypt*",
      "kms:GenerateDataKey*",
      "kms:DescribeKey"
    ]
    condition {
      test     = "StringEquals"
      variable = "kms:ViaService"
      values   = ["ec2.${data.aws_region.current.name}.amazonaws.com"]
    }
    condition {
      test     = "StringEquals"
      variable = "kms:CallerAccount"
      values   = ["${data.aws_caller_identity.current.account_id}"]
    }
    principals {
      type        = "*"
      identifiers = ["*"]
    }
  }

  statement {
    sid       = "Allow attachment of persistent resources"
    effect    = "Allow"
    resources = ["*"]

    actions = [
      "kms:CreateGrant",
      "kms:ListGrants",
      "kms:RevokeGrant",
    ]

    condition {
      test     = "Bool"
      variable = "kms:GrantIsForAWSResource"
      values   = ["true"]
    }
    condition {
      test     = "StringEquals"
      variable = "kms:ViaService"
      values   = ["ec2.${data.aws_region.current.name}.amazonaws.com"]
    }
    condition {
      test     = "StringEquals"
      variable = "kms:CallerAccount"
      values   = ["${data.aws_caller_identity.current.account_id}"]
    }
    principals {
      type        = "*"
      identifiers = ["*"]
    }
  }
}
