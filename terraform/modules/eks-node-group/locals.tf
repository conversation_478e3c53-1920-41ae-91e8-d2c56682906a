locals {
  ba_map = {
    "Enterprise Operations"      = "eo"
    "Aeronautics Company"        = "aero"
    "Missiles and Fire Control"  = "mfc"
    "Rotary and Mission Systems" = "rms"
    "Space"                      = "space"
  }
  common_tags = {
    CreatedDate = timestamp()
    ManagedBy   = "Terraform"
  }
  name_prefix     = "${var.cluster_name}-${var.node_group_name}"
  node_group_name = "${local.name_prefix}-node-group"
  required_lmco_tags = {
    lmsystemenvironment = var.environment
    lmbusinessarea      = lower(local.ba_map[var.business_area])
    lmcommonid          = var.common_id
    lmdrcriticality     = var.dr_criticality
    lmoptout            = var.optout
  }
  user_data_args = {
    cluster_name             = var.cluster_name
    https_proxy              = var.https_proxy
    lmco_cacert_download_url = var.lmco_cacert_download_url
    node_group_name          = local.node_group_name
    ntp_server               = var.ntp_server
    registry_authority       = var.registry_authority
  }
}

