MIME-Version: 1.0
Content-Type: multipart/mixed; boundary="==MYBOUNDARY=="

--==MYBOUNDARY==
Content-Type: text/x-shellscript; charset="us-ascii"

#!/bin/bash

function set_env() {
    # Specify container runtime
    export CONTAINER_RUNTIME="containerd"
    # Trust the LM combined certificate
    HTTPS_PROXY=${https_proxy} curl -k ${lmco_cacert_download_url} -o /etc/pki/ca-trust/source/anchors/Combined_pem.pem --cacert /etc/pki/ca-trust/source/anchors/base_cert.pem
    update-ca-trust extract
}
function mod_containerd_conf() {
# Modify the containerd configuration to redirect registries to a harbor proxy
mkdir -p /etc/containerd/certs.d
cat <<EOT >> /root/config-template.toml
version = 2
root = "/var/lib/containerd"
state = "/run/containerd"
[grpc]
address = "/run/containerd/containerd.sock"
[plugins."io.containerd.grpc.v1.cri".containerd]
default_runtime_name = "runc"
[plugins."io.containerd.grpc.v1.cri"]
sandbox_image = "SANDBOX_IMAGE"
[plugins."io.containerd.grpc.v1.cri".containerd.runtimes.runc]
runtime_type = "io.containerd.runc.v2"
[plugins."io.containerd.grpc.v1.cri".containerd.runtimes.runc.options]
SystemdCgroup = true
[plugins."io.containerd.grpc.v1.cri".cni]
bin_dir = "/opt/cni/bin"
conf_dir = "/etc/cni/net.d"
[plugins."io.containerd.grpc.v1.cri".registry]
config_path = "/etc/containerd/certs.d"
EOT

echo "server=\"https://${registry_authority}\"" > /etc/containerd/certs.d/_default/hosts.toml
echo "[host.\"https://${registry_authority}\"]\n  capabilities = [\"pull\", \"resolve\"]" >> /etc/containerd/certs.d/_default/hosts.toml
echo "ca = \"/etc/pki/ca-trust/source/anchors/Combined_pem.pem\"" >> /etc/containerd/certs.d/_default/hosts.toml
for host in \
  877085696533.dkr.ecr.af-south-1.amazonaws.com \
  800184023465.dkr.ecr.ap-east-1.amazonaws.com \
  602401143452.dkr.ecr.ap-northeast-1.amazonaws.com \
  602401143452.dkr.ecr.ap-northeast-2.amazonaws.com \
  602401143452.dkr.ecr.ap-northeast-3.amazonaws.com \
  602401143452.dkr.ecr.ap-south-1.amazonaws.com \
  602401143452.dkr.ecr.ap-southeast-1.amazonaws.com \
  602401143452.dkr.ecr.ap-southeast-2.amazonaws.com \
  296578399912.dkr.ecr.ap-southeast-3.amazonaws.com \
  602401143452.dkr.ecr.ca-central-1.amazonaws.com \
  602401143452.dkr.ecr.eu-central-1.amazonaws.com \
  602401143452.dkr.ecr.eu-north-1.amazonaws.com \
  590381155156.dkr.ecr.eu-south-1.amazonaws.com \
  602401143452.dkr.ecr.eu-west-1.amazonaws.com \
  602401143452.dkr.ecr.eu-west-2.amazonaws.com \
  602401143452.dkr.ecr.eu-west-3.amazonaws.com \
  558608220178.dkr.ecr.me-south-1.amazonaws.com \
  759879836304.dkr.ecr.me-central-1.amazonaws.com \
  602401143452.dkr.ecr.sa-east-1.amazonaws.com \
  602401143452.dkr.ecr.us-east-1.amazonaws.com \
  602401143452.dkr.ecr.us-east-2.amazonaws.com \
  151742754352.dkr.ecr.us-gov-east-1.amazonaws.com \
  013241004608.dkr.ecr.us-gov-west-1.amazonaws.com \
  602401143452.dkr.ecr.us-west-1.amazonaws.com \
  602401143452.dkr.ecr.us-west-2.amazonaws.com
do
    mkdir /etc/containerd/certs.d/$host
    touch /etc/containerd/certs.d/$host/hosts.toml
    echo "[host.\"https://${registry_authority}/v2/ext.public.ecr.aws\"]" >> /etc/containerd/certs.d/$host/hosts.toml
    echo "override_path = true" >> /etc/containerd/certs.d/$host/hosts.toml
    echo "capabilities = [\"pull\", \"resolve\"]" >> /etc/containerd/certs.d/$host/hosts.toml
    echo "ca = \"/etc/pki/ca-trust/source/anchors/Combined_pem.pem\"" >> /etc/containerd/certs.d/$host/hosts.toml
done
for host in \
  docker.elastic.co \
  gcr.io \
  ghcr.io \
  hub.docker.com \
  k8s.gcr.io \
  mcr.microsoft.com \
  projects.registry.vmware.com \
  public.ecr.aws \
  quay.io \
  registry1.dso.mil \
  registry.access.redhat.com \
  registry.aquasec.com \
  registry.gitlab.com \
  registry.redhat.io
do
    mkdir /etc/containerd/certs.d/$host
    touch /etc/containerd/certs.d/$host/hosts.toml
    echo "[host.\"https://${registry_authority}/v2/ext.$host\"]" >> /etc/containerd/certs.d/$host/hosts.toml
    echo "override_path = true" >> /etc/containerd/certs.d/$host/hosts.toml
    echo "capabilities = [\"pull\", \"resolve\"]" >> /etc/containerd/certs.d/$host/hosts.toml
    echo "ca = \"/etc/pki/ca-trust/source/anchors/Combined_pem.pem\"" >> /etc/containerd/certs.d/$host/hosts.toml
done
# redirect docker.io references to hub.docker.com proxy
mkdir /etc/containerd/certs.d/docker.io
touch /etc/containerd/certs.d/docker.io/hosts.toml
echo "[host.\"https://${registry_authority}/v2/ext.hub.docker.com\"]" >> /etc/containerd/certs.d/docker.io/hosts.toml
echo "override_path = true" >> /etc/containerd/certs.d/docker.io/hosts.toml
echo "capabilities = [\"pull\", \"resolve\"]" >> /etc/containerd/certs.d/docker.io/hosts.toml
echo "ca = \"/etc/pki/ca-trust/source/anchors/Combined_pem.pem\"" >> /etc/containerd/certs.d/docker.io/hosts.toml
for host in \
  harbor.global.lmco.com \
  harbor.us.lmco.com \
  ${registry_authority}
do
    mkdir /etc/containerd/certs.d/$host
    touch /etc/containerd/certs.d/$host/hosts.toml
    echo "[host.\"https://${registry_authority}\"]" >> /etc/containerd/certs.d/$host/hosts.toml
    echo "capabilities = [\"pull\", \"resolve\"]" >> /etc/containerd/certs.d/$host/hosts.toml
    echo "ca = \"/etc/pki/ca-trust/source/anchors/Combined_pem.pem\"" >> /etc/containerd/certs.d/$host/hosts.toml
done
}
function timeserver() {
# Enable NTP service using Chrony
yum erase 'ntp*'
yum install chrony
# Configure Chrony to prefer the configured NTP server
mv /etc/chrony.conf /etc/chrony.orig
echo "minsources 1"  >> /etc/chrony.conf
echo "rtcsync"  >> /etc/chrony.conf
for server in ${ntp_server}; do
  echo -e "server $server prefer iburst minpoll 4 maxpoll 4" >> /etc/chrony.conf
done
service chronyd restart
chkconfig chronyd on
}
function bootstrap() {
# Execute bootstrap.sh to complete initialization
set -ex
/etc/eks/bootstrap.sh ${cluster_name} \
    --container-runtime containerd \
    --containerd-config-file /root/config-template.toml \
    --kubelet-extra-args '--node-labels=eks.amazonaws.com/capacityType=ON_DEMAND,eks.amazonaws.com/nodegroup=${node_group_name}'
}
#########################################
#Begining of script
#########################################
set_env
mod_containerd_conf
timeserver
bootstrap
--==MYBOUNDARY==--