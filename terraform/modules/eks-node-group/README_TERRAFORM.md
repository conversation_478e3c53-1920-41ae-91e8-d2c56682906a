<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | ~>1.5.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~>5.33.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~>5.33.0 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_eks_node_group.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/eks_node_group) | resource |
| [aws_iam_role.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role_policy_attachment.amazon_ec2_container_registry_readonly](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.amazon_ec2_read_only_access](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.amazon_eks_cluster_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.amazon_eks_cni_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.amazon_eks_worker_node_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.common_deny](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_kms_alias.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/kms_alias) | resource |
| [aws_kms_key.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/kms_key) | resource |
| [aws_kms_key_policy.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/kms_key_policy) | resource |
| [aws_launch_template.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/launch_template) | resource |
| [aws_caller_identity.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/caller_identity) | data source |
| [aws_iam_policy.amazon_ec2_container_registry_readonly](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy) | data source |
| [aws_iam_policy.amazon_ec2_read_only_access](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy) | data source |
| [aws_iam_policy.amazon_eks_cluster_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy) | data source |
| [aws_iam_policy.amazon_eks_cni_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy) | data source |
| [aws_iam_policy.amazon_eks_worker_node_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy) | data source |
| [aws_iam_policy.common_deny](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy) | data source |
| [aws_iam_policy_document.assume_role_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |
| [aws_iam_policy_document.kms](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |
| [aws_iam_role.federated_user](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_role) | data source |
| [aws_partition.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/partition) | data source |
| [aws_region.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/region) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_business_area"></a> [business\_area](#input\_business\_area) | Business area full name (ex: Enterprise Operations) | `string` | n/a | yes |
| <a name="input_cluster_name"></a> [cluster\_name](#input\_cluster\_name) | The name of the cluster that will receive the node group | `string` | n/a | yes |
| <a name="input_common_id"></a> [common\_id](#input\_common\_id) | The CommonID associated with this instance | `string` | n/a | yes |
| <a name="input_create"></a> [create](#input\_create) | Determines whether to create the node group or not | `bool` | `true` | no |
| <a name="input_desired_size"></a> [desired\_size](#input\_desired\_size) | The number of Amazon EC2 instances that should be running in the autoscaling group | `number` | `1` | no |
| <a name="input_dr_criticality"></a> [dr\_criticality](#input\_dr\_criticality) | Determine resource’s DR Criticality level based on RPO and RTO requirements. Options are 0, 1, 2, or 3 | `number` | n/a | yes |
| <a name="input_environment"></a> [environment](#input\_environment) | Options are prd, test, dev, sandbox, qas | `string` | `""` | no |
| <a name="input_https_proxy"></a> [https\_proxy](#input\_https\_proxy) | The https proxy to use. Defaults to "". | `string` | `""` | no |
| <a name="input_instance_type"></a> [instance\_type](#input\_instance\_type) | The type of the EC2 instances hosting a worker node | `string` | `"t3.medium"` | no |
| <a name="input_lmco_cacert_download_url"></a> [lmco\_cacert\_download\_url](#input\_lmco\_cacert\_download\_url) | The url to get the Lockheed Martin certificate on EC2 node startup | `string` | `""` | no |
| <a name="input_max_size"></a> [max\_size](#input\_max\_size) | The max number of the autoscaling group worker nodes | `number` | `3` | no |
| <a name="input_min_size"></a> [min\_size](#input\_min\_size) | The min number of autoscaling group worker nodes | `number` | `1` | no |
| <a name="input_node_group_name"></a> [node\_group\_name](#input\_node\_group\_name) | The name of the node group being created | `string` | n/a | yes |
| <a name="input_node_group_subnet_ids"></a> [node\_group\_subnet\_ids](#input\_node\_group\_subnet\_ids) | A list of subnet IDs to launch resources in. Subnets automatically determine which availability zones the group will reside. | `list(string)` | n/a | yes |
| <a name="input_ntp_server"></a> [ntp\_server](#input\_ntp\_server) | The NTP server to which the worker nodes should synchronize their time. Defaults to the Amazon NTP server. | `string` | `"169.254.169.123"` | no |
| <a name="input_optout"></a> [optout](#input\_optout) | Used to support the opting out of cost optimization automation. Options are vmrightsizing, vmshutdown, vminstancescheduler (or a combination of them) | `string` | `"none"` | no |
| <a name="input_radm_role"></a> [radm\_role](#input\_radm\_role) | The name of the radm role that will assume the cluster admin role | `string` | n/a | yes |
| <a name="input_registry_authority"></a> [registry\_authority](#input\_registry\_authority) | The FQDN of a container registry to use as a proxy | `string` | `""` | no |
| <a name="input_resource_tags"></a> [resource\_tags](#input\_resource\_tags) | Additional Resource Tags | `map(string)` | `{}` | no |
| <a name="input_volume_size"></a> [volume\_size](#input\_volume\_size) | Size the OS drive | `number` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_aws_eks_node_group_name"></a> [aws\_eks\_node\_group\_name](#output\_aws\_eks\_node\_group\_name) | Name of the aws node group for to the cluster |
| <a name="output_aws_iam_role_arn"></a> [aws\_iam\_role\_arn](#output\_aws\_iam\_role\_arn) | IAM role Amazon Resource Name (ARN) of the Node Groups for the EKS Cluster |
| <a name="output_aws_iam_role_name"></a> [aws\_iam\_role\_name](#output\_aws\_iam\_role\_name) | IAM role name of the Node Groups for the EKS Cluster |
| <a name="output_aws_iam_role_policy_attachment_amazon_common_deny"></a> [aws\_iam\_role\_policy\_attachment\_amazon\_common\_deny](#output\_aws\_iam\_role\_policy\_attachment\_amazon\_common\_deny) | Required LMCO AWS Tag, added to code logic to avoid seeing changes that were expected, required in node creation process due to policy defintion restrictions |
| <a name="output_aws_iam_role_policy_attachment_amazon_eks_cluster_policy"></a> [aws\_iam\_role\_policy\_attachment\_amazon\_eks\_cluster\_policy](#output\_aws\_iam\_role\_policy\_attachment\_amazon\_eks\_cluster\_policy) | EC2 Amazon EKS Cluster Policy definition, required in node creation process due to policy defintion restrictions |
| <a name="output_aws_iam_role_policy_attachment_ec2_container_registry_readonly"></a> [aws\_iam\_role\_policy\_attachment\_ec2\_container\_registry\_readonly](#output\_aws\_iam\_role\_policy\_attachment\_ec2\_container\_registry\_readonly) | EC2 Container Registry Readonly Policy definition, to be consumed as a resource in IAM.tf |
| <a name="output_aws_iam_role_policy_attachment_ec2_read_only_access"></a> [aws\_iam\_role\_policy\_attachment\_ec2\_read\_only\_access](#output\_aws\_iam\_role\_policy\_attachment\_ec2\_read\_only\_access) | EC2 Read only access policy definition, to be consumed as a resource in IAM.tf |
| <a name="output_aws_iam_role_policy_attachment_eks_cni_policy"></a> [aws\_iam\_role\_policy\_attachment\_eks\_cni\_policy](#output\_aws\_iam\_role\_policy\_attachment\_eks\_cni\_policy) | EKS CNI Policy definition, to be consumed as a resource in IAM.tf |
| <a name="output_aws_iam_role_policy_attachment_eks_worker_node_policy"></a> [aws\_iam\_role\_policy\_attachment\_eks\_worker\_node\_policy](#output\_aws\_iam\_role\_policy\_attachment\_eks\_worker\_node\_policy) | EKS Worker Node Policy definition, to be consumed as a resource in IAM.tf |
| <a name="output_kms_key_alias"></a> [kms\_key\_alias](#output\_kms\_key\_alias) | The KMS key alias of the Node Groups for the EKS Cluster |
| <a name="output_kms_key_arn"></a> [kms\_key\_arn](#output\_kms\_key\_arn) | Amazon Resource Name (ARN) of the KMS key of the Node Groups for the EKS Cluster |
| <a name="output_kms_key_id"></a> [kms\_key\_id](#output\_kms\_key\_id) | ID of the KMS key of the Node Groups for the EKS Cluster |
| <a name="output_launch_templates"></a> [launch\_templates](#output\_launch\_templates) | Name of the Node Groups Launch Template |
| <a name="output_launch_templates_id"></a> [launch\_templates\_id](#output\_launch\_templates\_id) | ID of the Node Groups Launch Template |
| <a name="output_launch_templates_version"></a> [launch\_templates\_version](#output\_launch\_templates\_version) | Version of the Node Groups Launch Template |
<!-- END_TF_DOCS -->