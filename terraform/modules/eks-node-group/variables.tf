variable "create" {
  type        = bool
  default     = true
  description = "Determines whether to create the node group or not"
}

variable "resource_tags" {
  type        = map(string)
  description = "Additional Resource Tags"
  default     = {}
}


################################################################################
# AutoScaling Group
################################################################################

variable "desired_size" {
  type        = number
  default     = 1
  description = "The number of Amazon EC2 instances that should be running in the autoscaling group"
}

variable "max_size" {
  type        = number
  default     = 3
  description = "The max number of the autoscaling group worker nodes"
}

variable "min_size" {
  type        = number
  default     = 1
  description = "The min number of autoscaling group worker nodes"
}


################################################################################
# Launch Template
################################################################################

variable "instance_type" {
  type        = string
  default     = "t3.medium"
  description = "The type of the EC2 instances hosting a worker node"
}

variable "volume_size" {
  type        = number
  description = "Size the OS drive"
}


################################################################################
# User Data
################################################################################

variable "https_proxy" {
  type        = string
  default     = ""
  description = "The https proxy to use. Defaults to \"\"."
}

variable "lmco_cacert_download_url" {
  type        = string
  default     = ""
  description = "The url to get the Lockheed Martin certificate on EC2 node startup"
}

variable "ntp_server" {
  type        = string
  default     = "***************"
  description = "The NTP server to which the worker nodes should synchronize their time. Defaults to the Amazon NTP server."
}

variable "registry_authority" {
  type        = string
  description = "The FQDN of a container registry to use as a proxy"
  default     = ""
}


################################################################################
# Node Group
################################################################################

variable "cluster_name" {
  type        = string
  description = "The name of the cluster that will receive the node group"
}

variable "node_group_name" {
  type        = string
  description = "The name of the node group being created"
}

variable "node_group_subnet_ids" {
  type        = list(string)
  description = "A list of subnet IDs to launch resources in. Subnets automatically determine which availability zones the group will reside."
}


################################################################################
# KMS Key
################################################################################

variable "radm_role" {
  type        = string
  description = "The name of the radm role that will assume the cluster admin role"
}


################################################################################
# Required variables for FORCE Tagging
################################################################################

variable "business_area" {
  type        = string
  description = "Business area full name (ex: Enterprise Operations)"
  validation {
    condition     = contains(["Enterprise Operations", "Aeronautics Company", "Missiles and Fire Control", "Rotary and Mission Systems", "Space"], var.business_area)
    error_message = "Value of var.business_area should be \"Enterprise Operations\", \"Aeronautics Company\", \"Missiles and Fire Control\", \"Rotary and Mission Systems\", or \"Space\"."
  }
}

variable "common_id" {
  type        = string
  description = "The CommonID associated with this instance"

  validation {
    condition     = can(regex("\\d{9}", var.common_id))
    error_message = "CommonID should be a 9 digit string."
  }
}

variable "dr_criticality" {
  type        = number
  description = "Determine resource’s DR Criticality level based on RPO and RTO requirements. Options are 0, 1, 2, or 3"
  validation {
    condition     = contains([0, 1, 2, 3], var.dr_criticality)
    error_message = "Value of var.dr_criticality is not Valid. Options are 0, 1, 2, or 3."
  }
}

variable "environment" {
  type        = string
  description = "Options are prd, test, dev, sandbox, qas"
  default     = ""
  validation {
    condition     = contains(["prd", "test", "dev", "sbx", "qas"], var.environment)
    error_message = "Value of  var.environment is not Valid. Options are prd, test, dev, sbx, qas."
  }
}

variable "optout" {
  type        = string
  description = "Used to support the opting out of cost optimization automation. Options are vmrightsizing, vmshutdown, vminstancescheduler (or a combination of them)"
  default     = "none"
  validation {
    condition = anytrue([for which_outout in ["vmrightsizing", "vmshutdown", "vminstancescheduler"] :
      strcontains(var.optout, which_outout)
    ])
    error_message = "Value of var.optout is not Valid. Options are vmrightsizing, vmshutdown, vminstancescheduler (or a combination of them)."
  }
}
