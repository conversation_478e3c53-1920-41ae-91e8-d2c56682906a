#############################################################################################
#                                                                                           #
# Title:        common-main.tf                                                              #
# Version:                                                                                  #
#               2021-11-09 WRC. Add provider def to eliminate warnings in latest version    #
#               2021-08-13 WRC. Common service and Ba names to the map to support Space     #
#               2021-08-10 WRC. Create git common info based in PUB or gov                  #
#               2021-02-23 WRC. Add new code bucket for each region.                        #
#               2021-02-15 WRC. Add in the git values and the environments in region map    #
#               2020-12-08 WRC. Update cluster_name with new name                           #
#                               Remove CIS CMK references                                   # 
#               2020-12-03 WRC. Move the region_map to its own file for tracking            #
#                               Add Outputs section.  Change service_id to 'ocp'            #
#               2020-11-25 WRC. Update with Transit Gateway initial values for Pub          #
#               2020-11-18 WRC. Update with Transit Gateway IDs                             #
#               2020-11-03 WRC. Add common instance key pair to use for global stack        #
#               2020-09-18 WRC. Add third AZ to account ************/us-gov-west-1          #
#               2020-09-03 WRC. Add third AZ to account ************/us-gov-west-1          #
#               2020-07-29 WRC. Updated to support US GovCloud West                         #
#               2020-07-16 WRC. Updated to support additional OpenShift versions.           #
#               2020-06-09 WRC. Initial                                                     #
# Create Date:  2020-06-09                                                                  #
# <AUTHOR> <EMAIL>                          #
# Description:                                                                              #
#               Main resource file for the common module                                    #
#                                                                                           #
#############################################################################################
terraform { 
  required_providers { aws = {} }
}

#-------------------------------------------------------------------------------------------#
# Locals Section                                                                            #
#-------------------------------------------------------------------------------------------#
locals {
  business_area     = local.region_map[var.account].business_area       # "Enterprise Operations"
  business_org      = local.region_map[var.account].business_org        # "Infrastructure and International"
  service_org       = local.region_map[var.account].service_org         # "Enterprise Container Services"
  service_program   = local.region_map[var.account].service_program     # "Cloud Service"
  service_name      = local.region_map[var.account].service_name        # "OpenShift Container Platform"
  service_id        = local.region_map[var.account].service_id          # "ocp" # Used in OpenShift cluster_name
  prefix            = "${local.region_id}-${var.environment}"           # Used in OpenShift cluster_name
  prefix_global     = "${var.account}-${local.region_id}-${var.environment}"
  region_id         = local.region_map[var.account][var.region].region_id
 
  #-------------------------------------------------#
  # For timestamp functions below.  Each TF apply   #
  # needs a common time for the encire run.         #
  #-------------------------------------------------#
  timestamp     = timestamp()
 
  #-------------------------------------------------#
  # These are the common tags that are applied to   #
  # all resources.                                  #
  #-------------------------------------------------#
  tags_map = {
    BusinessArea        = local.business_area
    BusinessOrg         = local.business_org
    ServiceOrg          = local.service_org
    ServiceProgram      = local.service_program
    ServiceName         = local.service_name
    ServiceId           = local.service_id
    RegionId            = local.region_id
    AccountId           = var.account
    Region              = var.region
    Environment         = var.environment
  }
}

#-------------------------------------------------------------------------------------------#
# Data Section                                                                              #
#-------------------------------------------------------------------------------------------#
# 2020-12-09 REMOVE CIS CMK's since we have account CMKS now... delete later
# data "aws_kms_key" "LM_CloudTrail_CMK"  { key_id = "alias/LM_CloudTrail_CMK" }
# data "aws_kms_key" "LM_S3_CMK"          { key_id = "alias/LM_S3_CMK" }
# data "aws_kms_key" "LM_RDS_CMK"         { key_id = "alias/LM_RDS_CMK" }
# data "aws_kms_key" "LM_EBS_CMK"         { key_id = "alias/LM_EBS_CMK" }

data "aws_partition" "current" {}

#-----------------------------------------------#
# Gets the default VPC                          #
#-----------------------------------------------#
data "aws_vpc" "available" {
  state = "available"
}

#-------------------------------------------------------------------------------------------#
# Outputs Section                                                                           #
#-------------------------------------------------------------------------------------------#
output "bucket_tfstate" {
  value       = "${var.account}-${var.region}-$(var.environment}-tfstate"
  description = "The name of the actual bucket."
  sensitive   = false
}

#-----------------------------------------------#
# This section is for maps that are passed      #
# into the modules.                             #
#-----------------------------------------------#
output "common_data" {
  description = "The set of common data to make available to modules."
  sensitive   = false
  value = {
    aws_partition       = data.aws_partition.current.partition
#    subnet_public_list  = data.aws_subnet.public
#    subnet_public_num   = length(data.aws_subnet.public)
    business_area       = local.business_area
    business_area_id    = "eo"
    business_org        = local.business_org
    service_org         = local.service_org
    service_program     = local.service_program
    service_program_id  = "ocp"
    service_name        = local.service_name
    service_id          = local.service_id
    region_id           = local.region_id
    prefix              = local.prefix
    prefix_global       = local.prefix_global
    tags                = local.tags_map
    
    #-----------------------------------------------#
    # critical VPC common data                      #
    #-----------------------------------------------#
    vpc                 = data.aws_vpc.available
    vpc_id              = data.aws_vpc.available.id
    vpc_cidr_all        = "0.0.0.0/0"
    vpc_lmifw_cidr      = "10.77.77.10/32"
    
    #-----------------------------------------------#
    # Region Map data                               #
    #-----------------------------------------------#
    az_list             = local.region_map[var.account][var.region].az_list
    az_num              = length(local.region_map[var.account][var.region].az_list)
    cluster_name        = format("%s-%s", local.service_id, local.prefix)
    domain              = local.region_map[var.account][var.region].domain
    cidr_public         = local.region_map[var.account][var.region].cidr_public
    tgw                 = local.region_map[var.account][var.region].tgw
    default_rtb         = local.region_map[var.account][var.region].default_rtb
    environments        = local.region_map[var.account][var.region].environments
    accounts            = [ for key,value in local.region_map: format("%s", key) ]

    #---------------------------------------------------#
    # Standard set timestamp conversions. Create here so#
    # consistent across all modules during an update.   #
    #---------------------------------------------------#
    timestamp           = local.timestamp
    timestamp_year      = formatdate("YYYY", local.timestamp)
    timestamp_month     = formatdate("MM", local.timestamp)
    timestamp_month_day = formatdate("DD", local.timestamp)
    timestamp_time      = formatdate("HHmmss", local.timestamp)

    #-----------------------------------------------#
    # Keys                                          #
    #-----------------------------------------------#
    #LM_CloudTrail_CMK   = data.aws_kms_key.LM_CloudTrail_CMK
    #LM_S3_CMK           = data.aws_kms_key.LM_S3_CMK
    #LM_RDS_CMK          = data.aws_kms_key.LM_RDS_CMK
    #LM_EBS_CMK          = data.aws_kms_key.LM_EBS_CMK
    public_key          = "ssh-rsa AAAAB3NzaC1yc2EAAAABJQAAAQB8wYJZPKsw6TGv8qiX9etJTlhamMrcvXMo/OS5I90z3BB55C2xKycJPuL6cCmk6NNNFiZ3aLrTZ9o7cm3OIjBnz1B8+39CIqeFaxINMHsNMsxRnWb1xWFXvxOuJgblmGRt7UzbyqfasBX28SZ/T74xAm3FpKIYVdS+KdUjq4ha9uMWzTzTw+uWBVdS+MnQzA1h+qh265Z2XgYjG1KpG/5W3OUPnKXaM4KFCoh4FfOeULJOAAKTJganjBOiXAT9h44pV3rbHbOIfaSF9gSltdC40Nlu3LzFMsYrtX3HxaFUgejdTnG89qrg8toxXtMXZWgOHFV4F8H4o1m6SvEevxk3 rsa-key-********"

    #-----------------------------------------------#
    # Git Access for policies and user              #
    # 2021-08-10 WRC. if the partition is "aws"     #
    # then its PUB and not GOV.                     #
    #-----------------------------------------------#
    git_user                = "git"
    git_region_id           = data.aws_partition.current.partition == "aws" ? "upe1"         : "ugw1"
    git_account             = data.aws_partition.current.partition == "aws" ? "************" : "************"
    git_partition           = data.aws_partition.current.partition == "aws" ? "aws"          : "aws-us-gov"

    #-----------------------------------------------#
    # Standard set of buckets used by each          #
    # environment. Include ONLY those buckets used  #
    # in the envioronment... NOT tfstate bucket.    #
    # Create log bucket first.                      #
    #-----------------------------------------------#
    buckets = {
      logs                = "${local.prefix_global}-logs"       # Create the logs first
      backups             = "${local.prefix_global}-backups"
      bootstrap           = "${local.prefix_global}-bootstrap"
      registry            = "${local.prefix_global}-registry"
      code                = "${local.prefix_global}-code"
    }
  }
}


