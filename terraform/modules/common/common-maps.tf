#############################################################################################
#                                                                                           #
# Title:        common-maps.tf                                                              #
# Version:                                                                                  #
#               2022-05-23 JEZ. Add dv1, and dv[0-9]                                        #
#               2021-11-09 PTH. Add st0, and st[6-9] to ugw1-stg                            #
#               2021-11-09 WRC. Add Space additional clusters/environments to upe1          #
#               2021-09-17 WRC. Add Space VPCs for OCP CloudPak for Data                    #
#               2021-09-07 WRC. Add additional production environments PR1-PR5              #
#               2021-08-18 WRC. Create environment names for space to avoid collisions      #
#               2021-08-13 WRC. Common service and BA names to the map to support Space     #
#               2021-08-09 WRC. Add pubcloud east accounts.                                 #
#               2021-02-16 WRC. Add environments that are allowed in each account.          #
#                               Clean up region map to prevent hangs on bucket policy.      $
#               2020-12-08 WRC. Add US-GOV-EAST Accounts                                    #
#               2020-12-03 WRC. Contains the complete data map for all regions/envs         #
# Create Date:  2020-12-03                                                                  #
# <AUTHOR> <EMAIL>                          #
# Description:                                                                              #
#       This resource file region map contain all the common lookup data that is confured   #
#       by account and region.  ITS CRITICAL that the az_list order never changes as it     #
#       indexes all the resources and data.                                                 #
#                                                                                           #
#############################################################################################

#-------------------------------------------------------------------------------------------#
# Locals Section                                                                            #
#-------------------------------------------------------------------------------------------#
locals {

  region_map = {

#-------------------------------------------------------------------------------------------#
# AWS Account ************, Region: us-gov-west-1 Environment: eo.hpaas.radm                #
#-------------------------------------------------------------------------------------------#
    "************" = {
     
      #-------------------------#
      # ACCOUNT Common          #
      #-------------------------#
      business_area     = "Enterprise Operations"
      business_org      = "Infrastructure and International"
      service_org       = "Enterprise Container Services"
      service_program   = "Cloud Service"
      service_name      = "OpenShift Container Platform"
      service_id        = "ocp"
       
      #---------------------------------#
      # ************/us-gov-west-1      #
      #---------------------------------#
      us-gov-west-1 = {
        region_id           = "ugw1"
        domain              = "us.lmco.com"
        az_list             = [ "us-gov-west-1a",  "us-gov-west-1b",   "us-gov-west-1c" ]
        cidr_public         = [ "140.169.44.0/26", "140.169.44.64/26", "140.169.44.128/26" ]
        tgw                 = "tgw-021d4a1e8c2861732"       # Required for tgw-init module
        default_rtb         = "rtb-"       # VPC Default/Main route table automatically created with VPC 
      }
    }
    
#-------------------------------------------------------------------------------------------#
# AWS Account ************, Region: us-gov-west-1 Environment: eo.hpaasos.radm              #
#-------------------------------------------------------------------------------------------#
    "************" = {
          
      #-------------------------#
      # ACCOUNT Common          #
      #-------------------------#
      business_area     = "Enterprise Operations"
      business_org      = "Infrastructure and International"
      service_org       = "Enterprise Container Services"
      service_program   = "Cloud Service"
      service_name      = "OpenShift Container Platform"
      service_id        = "ocp"

      #---------------------------------#
      # ************/us-gov-west-1      #
      #---------------------------------#
      us-gov-west-1 = {
        region_id           = "ugw1"
        domain              = "us.lmco.com"
        az_list             = [ "us-gov-west-1a", "us-gov-west-1b", "us-gov-west-1c" ]
        cidr_public         = [ "146.69.142.128/26", "146.69.142.192/26", "146.69.135.0/26" ]
        tgw                 = "tgw-021d4a1e8c2861732"       # Required for tgw-init module
        default_rtb         = "rtb-"       # VPC Default/Main route table automatically created with VPC 

      }
    }
  
#-------------------------------------------------------------------------------------------#
# AWS Account ************, Region: us-gov-east-1 Environment: eo.hpocpge100.radm           #
#-------------------------------------------------------------------------------------------#
    "************" = {

      #-------------------------#
      # ACCOUNT Common          #
      #-------------------------#
      business_area     = "Enterprise Operations"
      business_org      = "Infrastructure and International"
      service_org       = "Enterprise Container Services"
      service_program   = "Cloud Service"
      service_name      = "OpenShift Container Platform"
      service_id        = "ocp"
      
      #---------------------------------#
      # ************/us-gov-east-1      #
      #---------------------------------#
      us-gov-east-1 = {
        region_id           = "uge1"
        domain              = "ecs.us.lmco.com"
        az_list             = [ "us-gov-east-1a",   "us-gov-east-1b",    "us-gov-east-1c" ]
        cidr_public         = [ "140.169.74.64/26", "140.169.74.128/26", "140.169.74.192/26" ]
        tgw                 = "tgw-03a2e79f971fa1086"       # Required for tgw-init module
        default_rtb         = "rtb-0a2056239c5129ce5"       # VPC Default/Main route table automatically created with VPC 
      }
    }
  
#-------------------------------------------------------------------------------------------#
# AWS Account ************, Region: us-pub-east-1 Environment: eo.hppt.radm                 #
#-------------------------------------------------------------------------------------------#
    "************" = {
      
      #-------------------------#
      # US East 1               #
      #-------------------------#
      "us-east-1" = {
        region_id           = "upe1"
        domain              = "us.lmco.com"
        az_list             = [ "us-east-1a", "us-east-1b" ]
        cidr_public         = [ "166.28.137.0/27", "166.28.137.32/27" ]
        tgw                 = ""  
        default_rtb         = ""
      }
      
      #-------------------------#
      # US West 1               #
      #-------------------------#
      "us-west-1" = {
        region_id           = "upw1"
        domain              = "global.lmco.com"
        az_list             = [ "us-west-1a", "us-west-1c" ]
        cidr_public         = [ "166.24.135.128/27","166.24.135.160/27" ]
        tgw                 = ""  
        default_rtb         = ""
      }
    }
    
    
#-------------------------------------------------------------------------------------------#
# AWS Account ************, Region: us-gov-west-1 Environment: OCP-UGW1-CPD/CPP (Space)     #
#-------------------------------------------------------------------------------------------#
    "************" = {

      #-------------------------#
      # ACCOUNT Common          #
      #-------------------------#
      business_area     = "Space"
      business_org      = "Information Technology"
      service_org       = "Information Technology"
      service_program   = "Space Hybrid Multi-Cloud Services"
      service_name      = "Space OpenShift Container Platform"
      service_id        = "ocp"  # Used in OpenShift cluster_name

      #-------------------------#
      # US GovCloud West 1      #
      #-------------------------#
      us-gov-west-1 = {
        environments        = toset([ "global", "cpd", "cpp", "cp1", "cp2" ])
        region_id           = "ugw1"
        domain              = "ecs.us.lmco.com"
        az_list             = [ "us-gov-west-1a",   "us-gov-west-1b",    "us-gov-west-1c"  ]
        cidr_public         = [ "140.169.54.64/26", "140.169.54.128/26", "140.169.54.192/26" ]
        tgw                 = "tgw-021d4a1e8c2861732"
        default_rtb         = "rtb-03c03f45084d8e90d"
      } 
    }    
    
#-------------------------------------------------------------------------------------------#
# AWS Account ************, Region: us-gov-east-1 Environment: OCP-UGE1-CPD/CPP (Space)     #
#-------------------------------------------------------------------------------------------#
    "************" = {

      #-------------------------#
      # ACCOUNT Common          #
      #-------------------------#
      business_area     = "Space"
      business_org      = "Information Technology"
      service_org       = "Information Technology"
      service_program   = "Space Hybrid Multi-Cloud Services"
      service_name      = "Space OpenShift Container Platform"
      service_id        = "ocp"  # Used in OpenShift cluster_name

      #-------------------------#
      # US GovCloud East 1      #
      #-------------------------#
      us-gov-east-1 = {
        environments        = toset([ "global", "cpd", "cpp", "cp1", "cp2" ])
        region_id           = "uge1"
        domain              = "ecs.us.lmco.com"
        az_list             = [ "us-gov-east-1a",  "us-gov-east-1b",   "us-gov-east-1c"  ]
        cidr_public         = [ "140.169.94.0/26", "140.169.94.64/26", "140.169.94.128/26" ]
        tgw                 = "tgw-03a2e79f971fa1086"
        default_rtb         = "rtb-0afa724e8121ac24d"
      } 
    }    
 
#-------------------------------------------------------------------------------------------#
# AWS Account ************, Region: us-east-1 Environment: OCP-UPE1-SSC/SS0-9 (Space)       #
#-------------------------------------------------------------------------------------------#
    "************" = {

      #-------------------------#
      # ACCOUNT Common          #
      #-------------------------#
      business_area     = "Space"
      business_org      = "Information Technology"
      service_org       = "Information Technology"
      service_program   = "Space Hybrid Multi-Cloud Services"
      service_name      = "Space OpenShift Container Platform"
      service_id        = "ocp"  # Used in OpenShift cluster_name

      #-------------------------#
      # US East 1               #
      #-------------------------#
      us-east-1 = {
        environments        = toset([ "global", "ssc", "ss0", "ss1", "ss2", "ss3", "ss4", "ss5", "ss6", "ss7", "ss8", "ss9" ]) # Use "ssX" for env to avoid DNS name collisions
        region_id           = "upe1"
        domain              = "ecs.us.lmco.com"
        az_list             = [ "us-east-1a",      "us-east-1b",       "us-east-1c"  ]
        cidr_public         = [ "166.28.35.64/26", "166.28.35.128/26", "166.28.139.128/26" ]
        tgw                 = "tgw-085c8679e54a3d159"
        default_rtb         = "rtb-01ac5931fd7d6f6d9"
      } 
    }

#-------------------------------------------------------------------------------------------#
# AWS Account ************, Region: us-east-1 Environment: OCP-UPE1-STG                     #
#-------------------------------------------------------------------------------------------#
    "************" = {
    
      #-------------------------#
      # ACCOUNT Common          #
      #-------------------------#
      business_area     = "Enterprise Operations"
      business_org      = "Infrastructure and International"
      service_org       = "Enterprise Container Services"
      service_program   = "Cloud Service"
      service_name      = "OpenShift Container Platform"
      service_id        = "ocp"
  
      #-------------------------#
      # US GovCloud West 1      #
      #-------------------------#
      us-east-1 = {
        environments        = toset([ "global", "stg", "st1", "st2", "st3", "st4", "st5", "snd", "sn1", "sn2", "sn3", "sn4", "sn5" ]) # Allowed in this account and region
        region_id           = "upe1"
        domain              = "ecs.us.lmco.com"
        az_list             = [ "us-east-1a",       "us-east-1b",        "us-east-1c"  ]
        cidr_public         = [ "166.28.140.64/26", "166.28.140.128/26", "166.28.140.192/26" ]
        tgw                 = "tgw-085c8679e54a3d159"
        default_rtb         = "rtb-03a436943a06e2c6c"
      } 
    }

#-------------------------------------------------------------------------------------------#
# AWS Account ************, Region: us-gov-west-1 Environment: OCP-UGW1-DEV                 #
#-------------------------------------------------------------------------------------------#
    "************" = {
    
      #-------------------------#
      # ACCOUNT Common          #
      #-------------------------#
      business_area     = "Enterprise Operations"
      business_org      = "Infrastructure and International"
      service_org       = "Enterprise Container Services"
      service_program   = "Cloud Service"
      service_name      = "OpenShift Container Platform"
      service_id        = "ocp"

      #-------------------------#
      # US GovCloud West 1      #
      #-------------------------#
      us-gov-west-1 = {
        environments        = toset([ "global", "dev" ])   # Allowed in this account and region
        region_id           = "ugw1"
        domain              = "ecs.us.lmco.com"
        az_list             = [ "us-gov-west-1a",   "us-gov-west-1b",     "us-gov-west-1c"  ]
        cidr_public         = [ "140.169.129.0/26", "140.169.130.128/26", "140.169.130.192/26" ]
        tgw                 = "tgw-021d4a1e8c2861732"
        default_rtb         = "rtb-05ca90a98be348245"
      } 
    }
    
#-------------------------------------------------------------------------------------------#
# AWS Account ************, Region: us-gov-west-1 Environment: OCP-UGW1-STG                 #
#-------------------------------------------------------------------------------------------#
    "************" = {
    
      #-------------------------#
      # ACCOUNT Common          #
      #-------------------------#
      business_area     = "Enterprise Operations"
      business_org      = "Infrastructure and International"
      service_org       = "Enterprise Container Services"
      service_program   = "Cloud Service"
      service_name      = "OpenShift Container Platform"
      service_id        = "ocp"

      #-------------------------#
      # US GovCloud West 1      #
      #-------------------------#
      us-gov-west-1 = {
        environments        = toset([ "global", "stg", "st0", "st1", "st2", "st3", "st4", "st5", "st6", "st7", "st8", "st9" ]) # Allowed in this account and region
        region_id           = "ugw1"
        domain              = "ecs.us.lmco.com"
        az_list             = [ "us-gov-west-1a",     "us-gov-west-1b",   "us-gov-west-1c" ]
        cidr_public         = [ "140.169.128.128/26", "140.169.130.0/26", "140.169.130.64/26" ]
        tgw                 = "tgw-021d4a1e8c2861732"
        default_rtb         = "rtb-0ab18817b00fa53fd"
      } 
    }

#-------------------------------------------------------------------------------------------#
# AWS Account ************, Region: us-gov-west-1 Environment: OCP-UGW1-PRD                 #
#-------------------------------------------------------------------------------------------#
    "************" = {
    
      #-------------------------#
      # ACCOUNT Common          #
      #-------------------------#
      business_area     = "Enterprise Operations"
      business_org      = "Infrastructure and International"
      service_org       = "Enterprise Container Services"
      service_program   = "Cloud Service"
      service_name      = "OpenShift Container Platform"
      service_id        = "ocp"

      #-------------------------#
      # US GovCloud West 1      #
      #-------------------------#
      us-gov-west-1 = {
        environments        = toset([ "global", "prd", "pr1", "pr2", "pr3", "pr4", "pr5" ])   # Allowed in this account and region
        region_id           = "ugw1"
        domain              = "ecs.us.lmco.com"
        az_list             = [ "us-gov-west-1a",     "us-gov-west-1b",   "us-gov-west-1c" ]
        cidr_public         = [ "140.169.128.192/26", "140.169.131.0/26", "140.169.131.64/26" ]
        tgw                 = "tgw-021d4a1e8c2861732"
        default_rtb         = "rtb-02997478445721f8d"
      } 
    }

#-------------------------------------------------------------------------------------------#
# AWS Account ************, Region: us-gov-east-1 Environment: OCP-UGE1-PRD (2020-12-05)    #
#-------------------------------------------------------------------------------------------#
    "************" = {
    
      #-------------------------#
      # ACCOUNT Common          #
      #-------------------------#
      business_area     = "Enterprise Operations"
      business_org      = "Infrastructure and International"
      service_org       = "Enterprise Container Services"
      service_program   = "Cloud Service"
      service_name      = "OpenShift Container Platform"
      service_id        = "ocp"

      #-------------------------#
      # US GovCloud West 1      #
      #-------------------------#
      us-gov-east-1 = {
        environments        = toset([ "global", "prd" ])   # Allowed in this account and region
        region_id           = "uge1"
        domain              = "ecs.us.lmco.com"
        az_list             = [ "us-gov-east-1a",     "us-gov-east-1b",   "us-gov-east-1c" ]
        cidr_public         = [ "140.169.80.128/26", "140.169.81.128/26", "140.169.81.192/26" ]
        tgw                 = "tgw-03a2e79f971fa1086"
        default_rtb         = "rtb-0ce8b09aa893371f1"
      } 
    }
    
#-------------------------------------------------------------------------------------------#
# AWS Account ************, Region: us-gov-east-1 Environment: OCP-UGE1-STG (2020-12-08)    #
#-------------------------------------------------------------------------------------------#
    "************" = {
    
      #-------------------------#
      # ACCOUNT Common          #
      #-------------------------#
      business_area     = "Enterprise Operations"
      business_org      = "Infrastructure and International"
      service_org       = "Enterprise Container Services"
      service_program   = "Cloud Service"
      service_name      = "OpenShift Container Platform"
      service_id        = "ocp"

      #-------------------------#
      # US GovCloud West 1      #
      #-------------------------#
      us-gov-east-1 = {
        environments        = toset([ "global", "stg", "st1", "st2", "st3", "st4", "st5" ]) # Allowed in this account and region
        region_id           = "uge1"
        domain              = "ecs.us.lmco.com"
        az_list             = [ "us-gov-east-1a",  "us-gov-east-1b",  "us-gov-east-1c" ]
        cidr_public         = [ "140.169.80.0/26", "140.169.82.0/26", "140.169.82.64/26" ]
        tgw                 = "tgw-03a2e79f971fa1086"   # TGW GOV-EAST
        default_rtb         = "rtb-073d76356fe8b2f40"   # VPC Default Route Table
      } 
    }    

#-------------------------------------------------------------------------------------------#
# AWS Account ************, Region: us-gov-east-1 Environment: OCP-UGE1-DEV (2020-12-08)    #
#-------------------------------------------------------------------------------------------#
    "************" = {
    
      #-------------------------#
      # ACCOUNT Common          #
      #-------------------------#
      business_area     = "Enterprise Operations"
      business_org      = "Infrastructure and International"
      service_org       = "Enterprise Container Services"
      service_program   = "Cloud Service"
      service_name      = "OpenShift Container Platform"
      service_id        = "ocp"

      #-------------------------#
      # US GovCloud West 1      #
      #-------------------------#
      us-gov-east-1 = {
        environments        = toset([ "global", "dev" ])   # Allowed in this account and region
        region_id           = "uge1"
        domain              = "ecs.us.lmco.com"
        az_list             = [ "us-gov-east-1a",   "us-gov-east-1b",    "us-gov-east-1c" ]
        cidr_public         = [ "140.169.80.64/26", "140.169.82.128/26", "140.169.82.192/26" ]
        tgw                 = "tgw-03a2e79f971fa1086"   # TGW GOV-EAST
        default_rtb         = "rtb-00bbfc739bcb61d28"   # VPC Default Route Table
      } 
    }    
  }
}	
