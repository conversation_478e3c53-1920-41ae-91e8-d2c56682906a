#############################################################################################
#                                                                                           #
# Title:        common-environments.tf                                                      #
# Version:                                                                                  #
#               2021-02-16 WRC. Initial                                                     #
# Create Date:  2021-02-16                                                                  #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                          #
# Description:                                                                              #
#       This resource file creates the map of environments that are allowed in this         #
#       build.  It was moved from within the AWS module because we need to use this to      #
#       create the credentials in the tfstate module.                                       #
#                                                                                           #
#############################################################################################

#-------------------------------------------------------------------------------------------#
# Locals Section                                                                            #
#-------------------------------------------------------------------------------------------#
locals {
  environment_index = var.environment
  environment_map = {
    "global"    = []    # to ensure that we have global covered
  #---------------------------------------------------------#
  # This map of CIDR blocks is for the private networks     #
  # only so is defined here.  NEVER change the order since  #
  # its indexed by the available zone list in common_data.  #
  #---------------------------------------------------------#
    "tst" = [ "100.64.32.0/22", "100.64.36.0/22", "100.64.40.0/22" ]
    "dev" = [ "100.64.44.0/22", "100.64.48.0/22", "100.64.52.0/22" ]
    "prd" = [ "100.64.56.0/22", "100.64.60.0/22", "100.64.64.0/22" ]
    "stg" = [ "100.64.68.0/22", "100.64.72.0/22", "100.64.76.0/22" ]
    "snd" = [ "100.64.80.0/22", "100.64.84.0/22", "100.64.88.0/22" ]

    #-------------------------------------------------------------------#
    # 2020-12-11 WRC. These networks can be smaller since they are for  #
    # ephemeral development and staging environments.                   #
    #-------------------------------------------------------------------#
    "dv1" = [ "100.64.104.0/24", "100.64.105.0/24", "100.64.106.0/24" ]
    "dv2" = [ "100.64.107.0/24", "100.64.108.0/24", "100.64.109.0/24" ]
    "dv3" = [ "100.64.110.0/24", "100.64.111.0/24", "100.64.112.0/24" ]
    "dv4" = [ "100.64.113.0/24", "100.64.114.0/24", "100.64.115.0/24" ]
    "dv5" = [ "100.64.116.0/24", "100.64.117.0/24", "100.64.118.0/24" ]
    #
    "st1" = [ "100.64.119.0/24", "100.64.120.0/24", "100.64.121.0/24" ]
    "st2" = [ "100.64.122.0/24", "100.64.123.0/24", "100.64.124.0/24" ]
    "st3" = [ "100.64.125.0/24", "100.64.126.0/24", "100.64.127.0/24" ]
    "st4" = [ "100.64.128.0/24", "100.64.129.0/24", "100.64.130.0/24" ]
    "st5" = [ "100.64.131.0/24", "100.64.132.0/24", "100.64.133.0/24" ]
    #
    "sn1" = [ "100.64.134.0/24", "100.64.135.0/24", "100.64.136.0/24" ]
    "sn2" = [ "100.64.137.0/24", "100.64.138.0/24", "100.64.139.0/24" ]
    "sn3" = [ "100.64.140.0/24", "100.64.141.0/24", "100.64.142.0/24" ]
    "sn4" = [ "100.64.143.0/24", "100.64.144.0/24", "100.64.145.0/24" ]
    "sn5" = [ "100.64.146.0/24", "100.64.147.0/24", "100.64.148.0/24" ]
  }
  
}	
