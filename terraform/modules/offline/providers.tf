# https://registry.terraform.io/namespaces/hashicorp
# https://somspeaks.com/terraform-offline-setup-and-initialization/
# terraform providers mirror C:\Users\<USER>\GitRepos\Galaxy\ansible_collections\lmco\aws\terraform\modules\plugins
# https://servian.dev/terraform-local-providers-and-registry-mirror-configuration-b963117dfffa
#   Note - using the file system mirror where the providers are installed via init and then made available as a directory
#   terraform init -plugin-dir /mnt/c/users/c1620/gitrepos/galaxy/ansible_collections/lmco/aws/terraform/modules/plugins


# filesystem_mirror {
#   path = "/mnt/c/users/c1620/gitrepos/galaxy/ansible_collections/lmco/aws/terraform/modules/plugins"
#   # include = ["local.providers/*/*"]
# }

# provider_installation {
#   dev_overrides {
#     registry.terraform.io/hashicorp/example = "/home/<USER>/customised-providers/terraform-provider-example"
#   }
# }

terraform {
  required_providers {
    archive = {
      source        = "hashicorp/archive"
      version       = "~> 2.4"
    }
    aws = {
      source        = "hashicorp/aws"
      version       = "~> 5.33"
    }
    azuread = {
      source        = "hashicorp/azuread"
      version       = "~> 2.47"
    }
    azurerm = {
      source        = "hashicorp/azurerm"
      version       = "~> 3.88"
    }
    azurestack = {
      source        = "hashicorp/azurestack"
      version       = "~> 1.0"
    }
    cloudinit = {
      source        = "hashicorp/cloudinit"
      version       = "~> 2.3"
    }
    consul = {
      source        = "hashicorp/consul"
      version       = "~> 2.20"
    }
    dns = {
      source        = "hashicorp/dns"
      version       = "~> 3.4"
    }
    external = {
      source        = "hashicorp/external"
      version       = "~> 2.3"
    }
    helm = {
      source        = "hashicorp/helm"
      version       = "~> 2.12"
    }
    http = {
      source        = "hashicorp/http"
      version       = "~> 3.4"
    }
    kubernetes = {
      source        = "hashicorp/kubernetes"
      version       = "~> 2.25"
    }
    local = {
      source        = "hashicorp/local"
      version       = "~> 2.4"
    }
    null = {
      source        = "hashicorp/null"
      version       = "~> 3.2"
    }
    random = {
      source        = "hashicorp/random"
      version       = "~> 3.6"
    }
    tfe = {
      source        = "hashicorp/tfe"
      version       = "~> 0.51"
    }
    time = {
      source        = "hashicorp/time"
      version       = "~> 0.10"
    }
    tls = {
      source        = "hashicorp/tls"
      version       = "~> 4.0"
    }
    vault = {
      source        = "hashicorp/vault"
      version       = "~> 3.24"
    }
  }
}
