---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        aws_tfstate_s3.tf                                                 #
# Version:                                                                        #
#               2024-01-27 WRC. Galaxy Integration Update                         #
#               2021-08-09 WRC. Map the S3 bucket policy for public and govcloud  #
#               2021-02-18 WRC. Refine bucket policy to include common-secrets.sh #
#               2021-02-16 WRC. Add bucket policy to get credentials              #
#               2020-01-19 WRC. Update settings for better protections            #
#               2020-12-04 WRC. Initial                                           #
# Create Date:  2020-12-04                                                        #
# Author:       <PERSON>grande, Wayne (US) <<EMAIL>>                #
# Description:                                                                    #
#               Established the S3 bucket this AWS account. This                  #
#               was derived from integration from the ECS terraform code.         #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: Tasks INPUT Variables
  vars:
    inputs:
      aws_common_state: "{{ aws_common_state }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: 2

# ------------------------------------------------------------------------------- #
- name: Create the AWS S3 Bucket
  amazon.aws.s3_bucket:
    state: "{{ aws_common_state }}"
    name: "{{ aws_prefix_global }}-tfstate"
    region: "{{ aws_common_vars.aws_default_region }}"
    access_key: "{{ aws_common_vars.aws_access_key_id }}"
    secret_key: "{{ aws_common_vars.aws_secret_access_key }}"
    validate_certs: "{{ aws_common_vars.validate_certs }}"

    # ------------------- #
    # Encryption          #
    # ------------------- #
    bucket_key_enabled: true  # Bucket Key encryption is only supported if encryption=aws:kms
    encryption: "aws:kms"
    encryption_key_id: "alias/{{ aws_prefix }}-{{ aws_iam_cmks.s3.id }}"

    # ------------------- #
    # Misc Options        #
    # ------------------- #
    acl: "private"
    delete_public_access: true # This option cannot be used together with a public_access definition
    dualstack: false
    force: false               # Delete all keys (including versions and delete markers) in the bucket first
    object_lock_enabled: false
    object_ownership: ObjectWriter
    # policy: "{{ aws_s3_bucket.policy }}" - TBD
    requester_pays: false
    validate_bucket_name: true  # Whether the bucket name should be validated to conform to AWS S3
    versioning: true

    # ------------------- #
    # Standard Tags       #
    # ------------------- #
    purge_tags: true            # Forces exact match of what is defined by tags parameter
    tags: "{{ aws_common_tags }}"

  # ------------------- #
  # Standard Attributes #
  # ------------------- #
  no_log: false
  register: aws_s3_bucket

# ------------------------------------------------------------------------------- #
- name: Debug print OUTPUT Variables
  vars:
    outputs:
      aws_s3_bucket: "{{ aws_s3_bucket }}"
  ansible.builtin.debug:
    var: outputs
    verbosity: 2
