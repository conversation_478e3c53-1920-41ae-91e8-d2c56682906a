---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        tasks/main.yml                                                    #
# Version:                                                                        #
#               2024-01-30 WRC. Galaxy Integration Update                         #
#               2024-01-30 WRC. Initial                                           #
# Create Date:  2024-01-30                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               Ansible main entry point for the tfstate role.                    #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: Tasks INPUT Variables
  vars:
    inputs:
      aws_iam_cmks: "{{ aws_iam_cmks }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: 2

# ------------------------------------------------------------------------------- #
- name: Execute tasks to create the Customer Master Keys (CMK)
  ansible.builtin.include_tasks: aws_tfstate_cmk.yml
  vars:
    aws_tfstate_cmk: "{{ item }}"
  loop: "{{ aws_iam_cmks | dict2items }}"

# ------------------------------------------------------------------------------- #
- name: Execute tasks to create the S3 Bucket
  ansible.builtin.include_tasks: aws_tfstate_s3.yml

# ------------------------------------------------------------------------------- #
- name: Execute tasks to create the DynamoDB table
  ansible.builtin.include_tasks: aws_tfstate_ddb.yml

# ------------------------------------------------------------------------------- #
- name: Execute tasks to create the IAM users
  ansible.builtin.include_tasks: aws_tfstate_iam.yml
