---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        tasks/aws_tfstate_iam.tf                                          #
# Version:                                                                        #
#               2024-02-06 WRC. Galaxy Initial                                    #
# Create Date:  2024-02-06                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               Established the terraform state iam bucket this AWS account. This #
#               was derived from integration from the ECS terraform code.         #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
# Attached CommonDeny
# 				"arn:aws:s3:::************-upe1-global-tfstate/upe1/global/*",
# 				"arn:aws:s3:::************-upe1-global-tfstate"
#         #arn:aws:iam::************:policy/CommonDeny

- name: Tasks INPUT Variables
  vars:
    inputs:
      aws_tfstate_vars: "{{ aws_tfstate_vars }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: 2

# ------------------------------------------------------------------------------- #
- name: Create the TFSTATE user
  amazon.aws.iam_user:
    state: "{{ aws_common_state }}"
    name: "{{ aws_prefix }}-tfstate"
    region: "{{ aws_common_vars.aws_default_region }}"
    access_key: "{{ aws_common_vars.aws_access_key_id }}"
    secret_key: "{{ aws_common_vars.aws_secret_access_key }}"
    validate_certs: "{{ aws_common_vars.validate_certs }}"
    remove_password: true  # Not interactive so delete user login passwords
    purge_policies: false
    wait: true
    wait_timeout: 60

    # ------------------- #
    # Standard tags       #
    # ------------------- #
    purge_tags: true
    tags: "{{ aws_common_tags }}"

  # ------------------- #
  # Standard Attributes #
  # ------------------- #
  no_log: false
  register: aws_iam_user
  check_mode: false   # Will only init if in this mode

# ------------------------------------------------------------------------------- #
# - name: Create the TFSTATE user policy
#   amazon.aws.iam_policy:
#     state: "{{ aws_common_state }}"
#     policy_name: "{{ aws_prefix }}-tfstate"
#     region: "{{ aws_common_vars.aws_default_region }}"
#     access_key: "{{ aws_common_vars.aws_access_key_id }}"
#     secret_key: "{{ aws_common_vars.aws_secret_access_key }}"
#     validate_certs: "{{ aws_common_vars.validate_certs }}"

#     iam_name: "{{ aws_prefix }}-tfstate"
#     iam_type: "user"
#     policy_json: "{{ aws_iam_user.policy | to_json }}"

#   # ------------------- #
#   # Standard Attributes #
#   # ------------------- #
#   no_log: false
#   register: aws_iam_policy_out
#   check_mode: true   # Will only init if in this mode

# ------------------------------------------------------------------------------- #
# The amazon.aws.iam_access_key module is not idempotent and will just keep       #
# creating new keys unless we test existance and active first.                    #
# Note: this will fail of the user does not exist (which should not happen) but   #
# will NOT fail of there are no keys.                                             #
# ------------------------------------------------------------------------------- #
- name: Get info on the TFSTATE user access key
  amazon.aws.iam_access_key_info:
    region: "{{ aws_common_vars.aws_default_region }}"
    access_key: "{{ aws_common_vars.aws_access_key_id }}"
    secret_key: "{{ aws_common_vars.aws_secret_access_key }}"
    validate_certs: "{{ aws_common_vars.validate_certs }}"
    user_name: "{{ aws_prefix }}-tfstate"

  # ------------------- #
  # Standard Attributes #
  # ------------------- #
  no_log: false
  register: aws_iam_access_key_info
  check_mode: false   # Will only init if in this mode

# ------------------------------------------------------------------------------- #
- name: Create the TFSTATE user access key if none active
  amazon.aws.iam_access_key:
    state: "{{ aws_common_state }}"
    region: "{{ aws_common_vars.aws_default_region }}"
    access_key: "{{ aws_common_vars.aws_access_key_id }}"
    secret_key: "{{ aws_common_vars.aws_secret_access_key }}"
    validate_certs: "{{ aws_common_vars.validate_certs }}"
    user_name: "{{ aws_prefix }}-tfstate"
    rotate_keys: true   # create a new key if needed

  # ------------------- #
  # Options             #
  # ------------------- #
  no_log: false
  register: aws_iam_access_key_out
  check_mode: true   # Will only init if in this mode

  # ------------------------------------- #
  # Condition                             #
  # Each user can have up to 2 keys. If   #
  # none are active, create new.          #
  # ------------------------------------- #
  when: aws_iam_access_key_info.access_keys | selectattr("status", "equalto", "Active") | list | length == 0

# ------------------------------------------------------------------------------- #
- name: PUT the credentials on the S3 bucket
  amazon.aws.s3_object:
    bucket: "{{ aws_prefix_global }}-tfstate"
    region: "{{ aws_common_vars.aws_default_region }}"
    access_key: "{{ aws_common_vars.aws_access_key_id }}"
    secret_key: "{{ aws_common_vars.aws_secret_access_key }}"
    validate_certs: "{{ aws_common_vars.validate_certs }}"

    object: credentials.init
    mode: put

    # ------------------------------------- #
    # Keep newlines (literal)               #
    # ------------------------------------- #
    content: "{{ lookup('template', 'templates/credentials.j2') }}"
      # [init]
      # aws_access_key_id="{{ ******************** access_key_id }}"
      # aws_secret_access_key=RsvSii1lfSqe/8v06zRkkIucJyq0LTGNgDWOSdJA
      # region=us-east-1
      # output=json
      # max_attempts=50

  # ------------------- #
  # Options             #
  # ------------------- #
  no_log: false
  check_mode: false   # Will only init if in this mode

# ------------------------------------------------------------------------------- #
- name: Debug print OUTPUT Variables
  vars:
    outputs:
      aws_iam_user_out: "{{ aws_iam_user_out }}"
      aws_iam_policy_out: "{{ aws_iam_policy_out }}"
      aws_iam_access_key_info_out: "{{ aws_iam_access_key_info_out }}"
      aws_iam_access_key_out: "{{ aws_iam_access_key_out }}"
  ansible.builtin.debug:
    var: outputs
    verbosity: 2
