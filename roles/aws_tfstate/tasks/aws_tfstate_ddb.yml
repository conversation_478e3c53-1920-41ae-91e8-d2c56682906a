---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        aws_tfstate_s3.tf                                                 #
# Version:                                                                        #
#               2024-01-27 WRC. Galaxy Integration Update                         #
#               2021-08-09 WRC. Map the S3 bucket policy for public and govcloud  #
#               2021-02-18 WRC. Refine bucket policy to include common-secrets.sh #
#               2021-02-16 WRC. Add bucket policy to get credentials              #
#               2020-01-19 WRC. Update settings for better protections            #
#               2020-12-04 WRC. Initial                                           #
# Create Date:  2020-12-04                                                        #
# <AUTHOR> <EMAIL>                #
# Description:                                                                    #
#               Established the terraform state S3 bucket this AWS account. This  #
#               was derived from integration from the ECS terraform code.         #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: Tasks INPUT Variables
  vars:
    inputs:
      aws_dynamodb_table: "{{ aws_dynamodb_table }}"
      aws_common_credentials: "{{ aws_common_credentials }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: 2

# ------------------------------------------------------------------------------- #
# Note there is no amazon.aws dynamoDB module at time of development
- name: Create the TFSTATE DynamoDB Table used for locking (using community.aws)
  community.aws.dynamodb_table:
    state: "{{ aws_common_state }}"
    name: "{{ aws_prefix }}-tfstate"
    region: "{{ aws_common_vars.aws_default_region }}"
    access_key: "{{ aws_common_vars.aws_access_key_id }}"
    secret_key: "{{ aws_common_vars.aws_secret_access_key }}"
    validate_certs: "{{ aws_common_vars.validate_certs }}"

    # ----------------------- #
    # Supported Parameters    #
    # ----------------------- #
    billing_mode: "PROVISIONED"
    hash_key_name: "LockID"
    hash_key_type: "STRING"
    table_class: "STANDARD"
    wait: true
    wait_timeout: 120

    # ----------------------- #
    # Capacity Attributes     #
    # ----------------------- #
    read_capacity: 1
    write_capacity: 1

    # ----------------------- #
    # Standard tags           #
    # ----------------------- #
    purge_tags: true
    tags: "{{ aws_common_tags }}"

  no_log: false
  register: aws_dynamodb_table
  check_mode: false

# ------------------------------------------------------------------------------- #
- name: Debug print OUTPUT Variables
  vars:
    outputs:
      aws_dynamodb_table: "{{ aws_dynamodb_table }}"
  ansible.builtin.debug:
    var: outputs
    verbosity: 2
