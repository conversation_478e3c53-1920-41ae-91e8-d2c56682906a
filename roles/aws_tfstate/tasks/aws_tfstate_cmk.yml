---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        aws_tfstate_cmk.tf                                                #
# Version:                                                                        #
#               2024-01-27 WRC. Galaxy Integration Update                         #
#               2024-01-29 WRC. Initial                                           #
# Create Date:  2024-01-29                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               Creates all the Customer Managed Keys {CMK's)                     #
#               for the VPC in preparation for Terraform usage and state          #
#               retention. This was set up to use a default policy document that  #
#               is defined as an Ansible dict and then passed as a JSON. In this  #
#               way, we can adjust and create unique policies if we need to.      #
#               way, we can adjust and create unique policies if we need to.      #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: Tasks INPUT Variables
  vars:
    inputs:
      aws_tfstate_cmk: "{{ aws_tfstate_cmk }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: 2

# ------------------------------------------------------------------------------- #
- name: Create a new KMS key
  amazon.aws.kms_key:
    state: "{{ aws_common_state }}"
    region: "{{ aws_common_vars.aws_default_region }}"
    access_key: "{{ aws_common_vars.aws_access_key_id }}"
    secret_key: "{{ aws_common_vars.aws_secret_access_key }}"
    validate_certs: "{{ aws_common_vars.validate_certs }}"

    alias: "alias/{{ aws_prefix }}-{{ aws_tfstate_cmk.key }}"   # One of alias or key_id are required.
    description: "Customer Managed Key for {{ aws_tfstate_cmk.key | upper }}"
    enabled: true
    enable_key_rotation: true
    key_spec:  "SYMMETRIC_DEFAULT"
    key_usage: "ENCRYPT_DECRYPT"
    multi_region: false
    pending_window: 7         # number of days between requesting deletion
    policy: "{{ aws_tfstate_cmk.value.policy | to_json }}"

    # ------------------- #
    # Standard tags       #
    # ------------------- #
    purge_tags: true    # existing tags will be purged from the resource to match exactly what is defined by tags parameter.
    tags: "{{ aws_common_tags }}"

  no_log: false
  register: aws_tfstate_kms_key
  check_mode: false

# ------------------------------------------------------------------------------- #
- name: Task OUTPUT Variables
  vars:
    outputs:
      aws_tfstate_kms_key: "{{ aws_tfstate_kms_key }}"
  ansible.builtin.debug:
    var: outputs
    verbosity: 2
