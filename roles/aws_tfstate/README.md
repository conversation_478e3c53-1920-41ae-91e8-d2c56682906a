# AWS Terraform State (lmco.aws.aws_tfstate)

This role is used to manage the Terraform State resources use to maintain the configuration and state data needed for a deployment of Terraform. Previously, AWS state resources had to be created semi-manually and then the state information moved the AWS resources (primarily S3 and DynamoDB). By using Ansible to orchestrate the resources needed for Terraform state, we are able to remove that manual step and prepare the AWS account for Terraform automation.

## Authors

This role is maintained by the [Lockheed Martin Galaxy Team](https://galaxy.pages.gitlab.global.lmco.com/documents/about/team/). Please see our [Feedback Page](https://galaxy.pages.gitlab.global.lmco.com/documents/feedback/) if you would like to pass along ideas or suggestions.

## Dependencies

Dependencies are defined at the collection level in the [Requirements](../../requirements.yml) configuration.

## Requirements

Ansible requirements are defined in the [Role Metadata](meta/main.yml) for this role.

## Parameters

Role variables are defined in the [Argument Specification](meta/argument_specs.yml) for this role and are listed by Entry Point.

## Example Playbook

This role contains a specific Entry Point that coordinates the execution of tasks. The will support invocation as part of a Galaxy bundle but may also be invoked directly using the following playbook definition.

```yaml
 tasks:
    - ansible.builtin.include_role:
        name: aws_tfstate
        tasks_from: aws_tfstate_main
```

## License

License terms are defined at the collection level in the [LICENSE](../../LICENSE) file.
