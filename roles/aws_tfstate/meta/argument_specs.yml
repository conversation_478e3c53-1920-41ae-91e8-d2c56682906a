---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        argument_specs.yml                                                #
# Version:                                                                        #
#               2024-01-24 WRC. Initial                                           #
# Create Date:  2024-01-24                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               This is the argument specification for the role. With this file   #
#               present, a new task is inserted at the beginning of role          #
#               execution that will validate the parameters supplied for the role #
#               against the specification. If the parameters fail validation, the #
#               role will fail execution.                                         #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

# Reference:
# https://docs.ansible.com/ansible/latest/collections/ansible/builtin/validate_argument_spec_module.html

argument_specs:
  main:
    author: <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>
    short_description: Galaxy Amazon Web Services Terraform State role entrypoint
    description:  Galaxy Amazon Web Services main entry point for the Terraform state
                  role is used to combines the discrete tasks in this role and build
                  out the Terraform state resources.

    # ----------------------------------------------- #
    # Options are “parameters” and mandatory          #
    # ----------------------------------------------- #
    options:
      aws_common_state: { type: str }
      aws_common_config: { type: dict }
      aws_tfstate_config:
        type: dict
        required: false  # Only needed if true.  If missing, the option is not required.
        no_log: false
        description:  This is the Galaxy Amazon Web Services variables dictionary defined
                      by the consumer at account initialization time.
