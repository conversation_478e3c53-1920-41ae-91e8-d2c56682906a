---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        test.yml                                                          #
# Version:                                                                        #
#               2024-03-05 Initial                                                #
# Create Date:  2024-03-05                                                        #
# Author:       <PERSON><PERSON>, <PERSON> (<EMAIL>)                             #
# Description:                                                                    #
#               Initial commit for test.yml                                       #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Test Play
  hosts: localhost
  remote_user: root
  roles:
    - { role: aws_tfstate, aws_common_state: present, aws_common_config: { max_attempts: 50 } }

 # ------------------------------------------------------------------------------- #
  tasks:
    - name: "Tasks OUTPUT Variables for {{ ansible_play_name }}"
      vars:
        outputs:
          aws_caller_info: "{{ aws_caller_info | default(None) }}"
          aws_common_defaults: "{{ aws_common_defaults | default(None) }}"
          aws_common_config: "{{ aws_common_config | default(None) }}"
          aws_common_vars: "{{ aws_common_vars | default(None) }}"
          aws_common_facts: "{{ aws_common_facts | default(None) }}"
      ansible.builtin.debug:
        var: outputs
        verbosity: 1
