---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        aws_tfstate_cmk                                                   #
# Version:                                                                        #
#               2024-01-24 WRC. Initial                                           #
# Create Date:  2024-01-23                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               This is the variable file that contains the CMK data that may     #
#               be passed into this module.                                       #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

# ----------------------------------------- #
# Dictionary structure for all CMKs         #
# ----------------------------------------- #
aws_iam_cmks:
  ddb:
    id: ddb
    name: Dynamo Database (DDB)
    policy: "{{ aws_iam_policy_default }}"
  ebs:
    id: ebs
    name: Amazon Elastic Block Store
    policy: "{{ aws_iam_policy_default }}"
  rds:
    id: rds
    name: Amazon Relational Database Service
    policy: "{{ aws_iam_policy_default }}"
  s3:
    id: s3
    name: Amazon S3 Object Storage
    policy: "{{ aws_iam_policy_s3 }}"
  ssm:
    id: ssb
    name: Amazon Systems Manager
    policy: "{{ aws_iam_policy_default }}"

# ----------------------------------------- #
# Dictionary structure for all policies     #
# ----------------------------------------- #
aws_iam_policy_default:
  Version: "2012-10-17"
  Statement:
    Sid: "Enable IAM User Permissions"
    Effect: "Allow"
    Action:
      - "kms:*"
    Resource:
      - "*"
    Principal:
      AWS: "arn:{{ aws_common_facts.partition }}:iam::{{ aws_common_facts.account }}:root"

# ----------------------------------------- #
# This policy supports access to common     #
# S3 buckets for GIT content. For now its   #
# just a mirror of default above, but ref:  #
# https://gitlab.us.lmco.com/ECS/openshift/-/blob/master/terraform/modules/tfstate/tfstate-cmk.tf
# ----------------------------------------- #
aws_iam_policy_s3:
  Version: "2012-10-17"
  Statement:
    Sid: "Allow S3 access for all principals in the account that are authorized"
    Effect: "Allow"
    Action:
      - "kms:*"
    Resource:
      - "*"
    Principal:
      AWS: "arn:{{ aws_common_facts.partition }}:iam::{{ aws_common_facts.account }}:root"
