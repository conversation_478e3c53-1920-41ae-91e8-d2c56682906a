---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2023-10-19 JGS                                                    #
#               2023-10-19 JGS. Initial                                           #
# Create Date:  2023-10-19                                                        #
# Author:       <PERSON>, <PERSON>.<EMAIL>                              #
# Description:                                                                    #
#               This file includes the infomation for galaxy.                     #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
# ------------------------------------------------------------------------------- #

# ------------------------------------------------------------------------------- #
# Galaxy Info Structure                                                           #
# ------------------------------------------------------------------------------- #
galaxy_info:
  author: Ian Hunter <<EMAIL>>
  description: aws Tests Role
  company: Lockheed Martin Galaxy Automation Team
  issue_tracker_url: https://ebstools-jira.us.lmco.com/projects/XBA_GALAXY_AUTO
  license: LICENSE
  min_ansible_version: "2.12.0"

  # ----------------------------------------------------------------------------- #
  # If this a Container Enabled role, provide minimum Ansible Container version   #
  # ----------------------------------------------------------------------------- #
  # min_ansible_container_version:

  # ----------------------------------------------------------------------------- #
  # Provide a list of supported platforms; for each platform a list of versions   #
  # If you don't wish to enumerate all versions for a platform, use 'all'.        #
  # To view available platforms and versions (or releases), visit:                #
  # https://galaxy.aansible rnsible.com/api/v1/platforms/                         #
  # ----------------------------------------------------------------------------- #
  platforms:
    - name: EL    # https://galaxy.ansible.com/api/v1/platforms/?page=6
      versions: [all]

# ------------------------------------------------------------------------------- #
# List tags for your role here, one per line. A tag is a keyword that describes   #
# and categorizes the role. Users find roles by searching for tags. Be sure to    #
# NOTE: A tag is limited to a single word comprised of alphanumeric characters.   #
#       Maximum 20 tags per role.                                                 #
# NOTE: WRC. Use the control structure to derive the tags                         #
# ------------------------------------------------------------------------------- #
  galaxy_tags:
    - aws

# ------------------------------------------------------------------------------- #
# Role dendencies here, one per line. Be sure to remove the '[]' above,           #
# if you add dependencies to this list.                                           #
# ------------------------------------------------------------------------------- #
# collections: [ lmco.galaxy ]
dependencies:
  - role: lmco.aws.aws_common

# ------------------------------------------------------------------------------- #
# Role Configuration                                                              #
# ------------------------------------------------------------------------------- #
allow_duplicates: false
