---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2024-03-05 Initial                                                #
# Create Date:  2024-03-05                                                        #
# Author:       <PERSON><PERSON>, <PERSON> (<EMAIL>)                             #
# Description:                                                                    #
#               Initial commit for main.yml                                       #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Galaxy_kubernetes
  ansible.builtin.debug:
    var: galaxy_kubernetes
    verbosity: 1

- name: Galaxy_kubernetes
  ansible.builtin.debug:
    var: galaxy_kubernetes
    verbosity: 1

- name: Openshift_profile
  ansible.builtin.debug:
    var: openshift_profile
    verbosity: 1

- name: Openshift_config
  ansible.builtin.debug:
    var: openshift_config
    verbosity: 1

- name: Hostvars
  ansible.builtin.debug:
    var: hostvars
    verbosity: 2

- name: Galaxy_group
  ansible.builtin.debug:
    var: galaxy_group
    verbosity: 1

- name: Galaxy_hosts
  ansible.builtin.debug:
    var: galaxy_hosts
    verbosity: 1

- name: Galaxy_host
  ansible.builtin.debug:
    var: galaxy_host
    verbosity: 1

- name: Galaxy_hosts_config
  ansible.builtin.debug:
    var: galaxy_hosts_config
    verbosity: 1

- name: Common_Config aws_common
  ansible.builtin.debug:
    var: aws_common
    verbosity: 1

- name: Galaxy Role tag
  ansible.builtin.debug:
    var: galaxy_role
    verbosity: 1

- name: Installation Validation
  ansible.builtin.include_tasks: installation_validation.yml
  when: "'installation_validation' in galaxy_role.tags"

- name: Molecule Tests
  ansible.builtin.include_tasks: molecule.yml
  when: "'molecule' in galaxy_role.tags"
