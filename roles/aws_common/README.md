# AWS Common (lmco.aws.aws_common)

This role contains Ansible resources that are common across all of the roles within this collection. This typically will include defaults and variables but may also include tasks and modules if appropriate.

## Authors

This role is maintained by the [Lockheed Martin Galaxy Team](https://galaxy.pages.gitlab.global.lmco.com/documents/about/team/). Please see our [Feedback Page](https://galaxy.pages.gitlab.global.lmco.com/documents/feedback/) if you would like to pass along ideas or suggestions.

## Dependencies

Dependencies are defined at the collection level in the [Requirements](../../requirements.yml) configuration.

## Requirements

Ansible requirements are defined in the [Role Metadata](meta\main.yml) for this role.

## Parameters

Role variables are defined in the [Argument Specification](meta\argument_specs.yml) for this role and are listed by Entry Point.

## Example Playbook

This role is not typically invoked from a playbook but is instead referenced in the meta/main.yml of other roles in the collection. However, for testing, the following may be used to invoke this role directly.

```yaml

    - hosts: servers
      roles:
         - { role: lmco.aws.aws_common }
```

## License

License terms are defined at the collection level in the [LICENSE](../../LICENSE) file.
