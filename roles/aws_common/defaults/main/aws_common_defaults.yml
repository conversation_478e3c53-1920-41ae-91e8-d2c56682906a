---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        defaults/aws_common_defaults.yml                                  #
# Version:                                                                        #
#               2024-02-05 WRC. Galaxy Integration Updates                        #
#               2024-01-29 WRC. Initial                                           #
# Create Date:  2024-01-29                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               Default values required for the AWS collection to ensure that     #
#               it can run standalone.                                            #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
# ------------------------------------------- #
# Default values for ansible debugging when   #
# running as standalone collection.           #
# ------------------------------------------- #
ansible_common_debug:
  task_entry:
    name: Task file INPUT variables
    verbosity: 1
  task_exit:
    name: Task file OUTPUT variables
    verbosity: 1
