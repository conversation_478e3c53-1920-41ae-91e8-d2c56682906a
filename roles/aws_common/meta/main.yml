---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2024-03-05 Initial                                                #
# Create Date:  2024-03-05                                                        #
# Author:       <PERSON><PERSON>, <PERSON> (<EMAIL>)                             #
# Description:                                                                    #
#               Initial commit for main.yml                                       #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

# ------------------------------------------------------------------------------- #
# Galaxy Info Structure                                                           #
# ------------------------------------------------------------------------------- #
galaxy_info:
  author: dl-EIT, Galaxy <<EMAIL>>
  description: Amazon Web Services (AWS) Common Role
  company: Lockheed Martin Galaxy Automation Team
  issue_tracker_url: https://ebstools-jira.us.lmco.com/projects/XBA_GALAXY_AUTO
  license: LICENSE
  min_ansible_version: "2.15.0"

  # ----------------------------------------------------------------------------- #
  # If this a Container Enabled role, provide minimum Ansible Container version   #
  # ----------------------------------------------------------------------------- #
  # min_ansible_container_version:

  # ----------------------------------------------------------------------------- #
  # Provide a list of supported platforms; for each platform a list of versions   #
  # If you don't wish to enumerate all versions for a platform, use 'all'.        #
  # To view available platforms and versions (or releases), visit:                #
  # https://galaxy.ansible.com/api/v1/platforms/                                  #
  # https://galaxy.ansible.com/api/v1/platforms/?page=6                           #
  # ----------------------------------------------------------------------------- #
  platforms:
    - name: EL
      versions: ["all"]

  # ----------------------------------------------------------------------------- #
  # List tags for your role here, one per line. A tag is a keyword that describes #
  # and categorizes the role. Users find roles by searching for tags. Be sure to  #
  # NOTE: A tag is limited to a single word comprised of alphanumeric characters. #
  #       Maximum 20 tags per role.                                               #
  # NOTE: WRC. Use the control structure to derive the tags                       #
  # ----------------------------------------------------------------------------- #
  galaxy_tags:
    - lmco
    - ansible
    - automation
    - common
    - terraform

# ------------------------------------------------------------------------------- #
# Role dependencies here, one per line. Be sure to remove the '[]' above,         #
# if you add dependencies to this list.                                           #
# ------------------------------------------------------------------------------- #


# ------------------------------------------------------------------------------- #
# Role Configuration                                                              #
# ------------------------------------------------------------------------------- #
allow_duplicates: false
