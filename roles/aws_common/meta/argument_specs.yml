---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        argument_specs.yml                                                #
# Version:                                                                        #
#               2024-01-24 WRC. Initial                                           #
# Create Date:  2024-01-24                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               This is the argument specification for the role. With this file   #
#               present, a new task is inserted at the beginning of role          #
#               execution that will validate the parameters supplied for the role #
#               against the specification. If the parameters fail validation, the #
#               role will fail execution. Reference:                              #
#               NOTE: Option Defaults DO NOT work for role argument_specs         #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
# https://docs.ansible.com/ansible/latest/collections/ansible/builtin/validate_argument_spec_module.html
# https://docs.ansible.com/ansible/latest/playbook_guide/playbooks_reuse_roles.html

argument_specs:
  main:
    author: dl-EIT, Galaxy <<EMAIL>>
    short_description: Galaxy Amazon Web Services Collection
    description:  Galaxy Amazon Web Services products and services are provisioned
                  through this collection.
    version_added: 1.0.0

    # ----------------------------------------------- #
    # Options are “parameters” or “variables”.        #
    # ----------------------------------------------- #
    options:
      aws_common_state:
        type: str
        required: true
        no_log: false
        description:  This is the Galaxy general purpose state for resources that determines
                      the relevant actions to take. In most cases, this will be present or absent
                      but in other cases it will be used to drive an action.
        choices: [present]

      aws_common_config:
        type: dict
        # required: true  # Only needed if true.  If missing, the option is not required.
        no_log: false
        description:  This is the Galaxy Amazon Web Services credentials that is defined
                      by the consumer at account initialization time. It must provide access
                      to all resources in the VPC so that the initialization code can setup
                      credentials that will be used by the provisioner.

        # ----------------------------------------------- #
        # Options are “parameters” and mandatory          #
        # ----------------------------------------------- #
        options:
          aws_access_key_id:
            description: Standard AWS Access Key (AWS_ACCESS_KEY_ID)
            type: str
          aws_secret_access_key:
            description: Standard AWS Secret Access Key (AWS_SECRET_ACCESS_KEY)
            type: str
          region:
            description: Standard AWS Default Region (AWS_DEFAULT_REGION)
            type: str
            default: US-EAST-1
          output:
            description: Standard AWS Default Output (AWS_DEFAULT_OUTPUT)
            type: str
            default: "json"
            choices: [json]
          max_attempts:
            description: AWS Max attempts to retry access credentials (AWS_MAX_ATTEMPTS)
            type: int
            choices: [50]
