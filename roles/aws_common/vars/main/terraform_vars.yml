---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        terraform_vars.yml                                                #
# Version:                                                                        #
#               2024-01-23 WRC. Initial                                           #
# Create Date:  2024-01-23                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               This is the variable file that contains the globals for the       #
#               Terraform collection not defined in other variables files. These  #
#               values should be considered "constants" in most cases and         #
#               may be intentionally overridden by profile values.                #
#                                                                                 #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

# WRC - note, this variable file is used to deploy the providers and should be moved
# to the terraform collection
terraform_common:
  version: "1.7.0"

terraform_providers:
  archive:
    name: archive
    source: "hashicorp"
    version: "~> 2.4"
  aws:
    name: aws
    source: "hashicorp"
    version: "~> 5.33"
  azuread:
    name: azuread
    source: "hashicorp"
    version: "~> 2.47"
  azurerm:
    name: azurerm
    source: "hashicorp"
    version: "~> 3.88"
  azurestack:
    name: azurestack
    source: hashicorp
    version: "~> 1.0"
  cloudinit:
    name: cloudinit
    source: hashicorp
    version: "~> 2.3"
  consul:
    name: consul
    source: hashicorp
    version: "~> 2.20"
  dns:
    name: dns
    source: hashicorp
    version: "~> 3.4"
  local:
    name: local
    source: hashicorp
    version: "~> 2.4"
  external:
    name: external
    source: hashicorp
    version: "~> 2.3"
  helm:
    name: helm
    source: hashicorp
    version: "~> 2.12"
#     }
#     http = {
#       source        = "hashicorp/http"
#       version       = "~> 3.4"
#     }
#     kubernetes = {
#       source        = "hashicorp/kubernetes"
#       version       = "~> 2.25"
#     }

#     null = {
#       source        = "hashicorp/null"
#       version       = "~> 3.2"
#     }
#     random = {
#       source        = "hashicorp/random"
#       version       = "~> 3.6"
#     }
#     tfe = {
#       source        = "hashicorp/tfe"
#       version       = "~> 0.51"
#     }
#     time = {
#       source        = "hashicorp/time"
#       version       = "~> 0.10"
#     }
#     tls = {
#       source        = "hashicorp/tls"
#       version       = "~> 4.0"
#     }
#     vault = {
#       source        = "hashicorp/vault"
#       version       = "~> 3.24"
#     }
#   }
# }
