---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        vars/aws_common_vars                                              #
# Version:                                                                        #
#               2024-01-24 WRC. Initial                                           #
# Create Date:  2024-01-23                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               This is the common variable file that contains the defaults       #
#               and the merging of the inbound configuration dictionary           #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

# ------------------------------------------------------------------------------- #
# Get current time so that the modules have a single time snapshot                #
# ------------------------------------------------------------------------------- #
aws_timestamp: "{{ ansible_date_time }}"

# ------------------------------------------------------------------------------- #
# Values that need to be available in the common data dictionary                  #
# ------------------------------------------------------------------------------- #
aws_prefix: "{{ aws_common_facts.region_id }}-{{ aws_common_vars.environment }}"
aws_prefix_global: "{{ aws_common_facts.account }}-{{ aws_prefix }}"

# ------------------------------------------------------------------------------- #
# AWS Common Default Dictionary                                                   #
# ------------------------------------------------------------------------------- #
aws_common_defaults:
  aws_access_name: "Galaxy Default Intialization Key"
  aws_access_key_id: "{{ lookup('ansible.builtin.env', 'AWS_ACCESS_KEY_ID', default='') }}"
  aws_secret_access_key: "{{ lookup('ansible.builtin.env', 'AWS_SECRET_ACCESS_KEY', default='') }}"
  max_attempts: "{{ lookup('ansible.builtin.env', 'AWS_MAX_ATTEMPTS', default=50) | int }}" # Ensure its an INT
  aws_ca_bundle: "{{ lookup('ansible.builtin.env', 'AWS_CA_BUNDLE', default='') }}"
  aws_config_file: "{{ lookup('ansible.builtin.env', 'AWS_CONFIG_FILE', default='us-east-1') }}"
  aws_default_output: "{{ lookup('ansible.builtin.env', 'AWS_DEFAULT_OUTPUT', default='json') }}"
  aws_default_region: "{{ lookup('ansible.builtin.env', 'AWS_DEFAULT_REGION', default='us-east-1') }}"
  aws_profile: "{{ lookup('ansible.builtin.env', 'AWS_PROFILE', default='init') }}"
  aws_shared_credentials_file: "{{ lookup('ansible.builtin.env', 'AWS_SHARED_CREDENTIALS_FILE', default='~/.aws/credentials') }}"
  aws_credentials_file: "credentials"
  environment: globl
  environment_name: Galaxy Global Environment

  # --------------------------------- #
  # Common AWS Options                #
  # --------------------------------- #
  validate_certs: true

  # --------------------------------- #
  # Standard Attributes               #
  # --------------------------------- #
  no_log: false
  check_mode: true   # Will only init if in this mode

# ------------------------------------------------------------------------------- #
# AWS Common Variables from defaults and inbound configuration overrides          #
# ------------------------------------------------------------------------------- #
aws_common_vars: "{{ aws_common_defaults | ansible.builtin.combine(aws_common_config, recursive=true) }}"
