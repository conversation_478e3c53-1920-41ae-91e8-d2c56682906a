---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        aws_common/vars/aws_vars.yml                                      #
# Version:                                                                        #
#               2024-01-23 WRC. Initial                                           #
# Create Date:  2024-01-23                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               This is the variable file that contains the globals for the       #
#               AWS collection not defined in other variables files. These        #
#               values should be considered "constants" in most cases and         #
#               may be intentionally overridden by profile values.                #
#                                                                                 #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

# ------------------------------------------------------------------------------- #
# AWS Common Data passed into the Terraform modules.                              #
# ------------------------------------------------------------------------------- #
aws_common_data:

  # --------------------------------------------- #
  # Key AWS account index data                    #
  # --------------------------------------------- #
  account: "{{ aws_account.id }}"
  region: "{{ aws_region.id }}"
  environment: "{{ aws_environment.id }}"

  # --------------------------------------------- #
  # AWS partition data                            #
  # --------------------------------------------- #
  aws_partition: "{{ aws_region.partition }}"

  # --------------------------------------------- #
  # Critical Program Data                         #
  # --------------------------------------------- #
  business_area: Space
  business_area_id: space
  business_org: "Information Technology & Digital Enablement"   # Organization under the business_area
  service_org: "Platform Services"                              # Organization under the business_org
  service_program: Galaxy                                       # Program under the service_org
  service_program_id: glxy                                       # Program under the service_org
  service_name: Galaxy Services
  service_id: glxy

  # --------------------------------------------- #
  # Values used for naming                        #
  # --------------------------------------------- #
  region_id: "{{ aws_region.id }}"
  prefix: "{{ aws_prefix }}"
  prefix_global: "{{ aws_prefix_global }}"

  # --------------------------------------------- #
  # AWS Resource Tags                             #
  # --------------------------------------------- #
  tags: "{{ aws_tags }}"      # Located in AWS tags variable file

  # --------------------------------------------- #
  # critical VPC common data                      #
  # --------------------------------------------- #
  vpc: TBD #                = data.aws_vpc.available
  vpc_id: TBD #              = data.aws_vpc.available.id
  vpc_cidr_all: "0.0.0.0/0"
  vpc_lmifw_cidr: "10.77.77.10/32"

  # --------------------------------------------- #
  # Critical Region Data                          #
  # --------------------------------------------- #
  # az_list:             = local.region_map[var.account][var.region].az_list
  # az_num:              = length(local.region_map[var.account][var.region].az_list)
  # cluster_name:        = format("%s-%s", local.service_id, local.prefix)
  # domain:              = local.region_map[var.account][var.region].domain
  # cidr_public:         = local.region_map[var.account][var.region].cidr_public
  # tgw:                 = local.region_map[var.account][var.region].tgw
  # default_rtb:         = local.region_map[var.account][var.region].default_rtb
  # environments:        = local.region_map[var.account][var.region].environments
  # accounts:            = [ for key,value in local.region_map: format("%s", key) ] # May not need

  # ------------------------------------------------- #
  # Standard set timestamp conversions. Create here so#
  # consistent across all modules during an update.   #
  # ------------------------------------------------- #
  timestamp:            "{{ aws_timestamp }}"
  timestamp_year:       "{{ aws_timestamp.year }}"
  timestamp_month:      "{{ aws_timestamp.month }}"
  timestamp_month_day:  "{{ aws_timestamp.day }}"
  timestamp_time:       "{{ aws_timestamp.time }}"

  # --------------------------------------------- #
  # Keys                                          #
  # --------------------------------------------- #
  # LM_CloudTrail_CMK   = data.aws_kms_key.LM_CloudTrail_CMK
  # LM_S3_CMK           = data.aws_kms_key.LM_S3_CMK
  # LM_RDS_CMK          = data.aws_kms_key.LM_RDS_CMK
  # LM_EBS_CMK          = data.aws_kms_key.LM_EBS_CMK
  public_key: >-
    "ssh-rsa
      AAAAB3NzaC1yc2EAAAABJQAAAQB8wYJZPKsw6TGv8qiX9etJTlhamMrcvXMo/OS5I90z3BB55C2xKycJ
      PuL6cCmk6NNNFiZ3aLrTZ9o7cm3OIjBnz1B8+39CIqeFaxINMHsNMsxRnWb1xWFXvxOuJgblmGRt7Uzb
      yqfasBX28SZ/T74xAm3FpKIYVdS+KdUjq4ha9uMWzTzTw+uWBVdS+MnQzA1h+qh265Z2XgYjG1KpG/5W
      3OUPnKXaM4KFCoh4FfOeULJOAAKTJganjBOiXAT9h44pV3rbHbOIfaSF9gSltdC40Nlu3LzFMsYrtX3H
      xaFUgejdTnG89qrg8toxXtMXZWgOHFV4F8H4o1m6SvEevxk3 rsa-key-********"

  # --------------------------------------------- #
  # Git Access for policies and user              #
  # 2021-08-10 WRC. if the partition is "aws"     #
  # then its PUB and not GOV.                     #
  # --------------------------------------------- #
  git_user:       "git"
  git_region_id:  "upe1"          # data.aws_partition.current.partition == "aws" ? "upe1"         : "ugw1"
  git_account:    "************"  # data.aws_partition.current.partition == "aws" ? "************" : "************"
  git_partition:  "aws"           # data.aws_partition.current.partition == "aws" ? "aws"          : "aws-us-gov"

  # --------------------------------------------- #
  # Standard set of buckets used by each          #
  # environment. Include ONLY those buckets used  #
  # in the envioronment... NOT tfstate bucket.    #
  # Create log bucket first.                      #
  # --------------------------------------------- #
  buckets:
    logs:       "{{ aws_prefix_global }}-logs"       # Create the logs first
    backups:    "{{ aws_prefix_global }}-backups"
    bootstrap:  "{{ aws_prefix_global }}-bootstrap"
    registry:   "{{ aws_prefix_global }}-registry"
    code:       "{{ aws_prefix_global }}-code"
