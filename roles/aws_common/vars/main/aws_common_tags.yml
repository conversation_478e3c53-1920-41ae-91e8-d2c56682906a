---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        aws_tags.yml                                                      #
# Version:                                                                        #
#               2024-01-23 WRC. Initial                                           #
# Create Date:  2024-01-23                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               This is the variable file that contains the globals for the       #
#               AWS collection not defined in other variables files.              #
#               Reference following for compliance:                               #
#               https://docs.us.lmco.com/display/I2D2/Tag+Governance              #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
# Notes:
# Each tag consists of a key and an optional value, both of which you define.
# Tag keys can have a maximum character length of 128 characters, and tag values
# can have a maximum length of 256 characters.

# validation constraint pattern for tags [\p{L}\p{Z}\p{N}_.:/=+\-@]*'

# ----------------------------------------- #
# Used to translate standard Galaxy tags    #
# to required EO/FORCE tags for compliance. #
# ----------------------------------------- #
aws_common_tags_map:
  dev: dev
  gbl: prd
  prd: prd
  qas: qa
  snd: sbx
  tst: tst

aws_common_tags:

  # ----------------------------------------- #
  # Tags that are required for EO compliance  #
  # ----------------------------------------- #
  lmsystemenvironment: "{{ aws_tags_map['gbl'] | default('sbx') }}"   # Default to sandbox
  lmbusinessarea: "space"       # space, aero, mfc, rms, eo
  lmcommonid: "*********"
  lmdrcriticality: "3"          # 0,1,2,3
  lmoptout: "none"              # none, vmrightsizing;vmshutdown;vminstancescheduler
  lmmaintenancewindow: "TBD"

  # ----------------------------------------- #
  # Tags that Galaxy specific                 #
  # ----------------------------------------- #
  BusinessArea: "Space"                             # "Enterprise Operations"
  BusinessOrg: "Information Technology and Digital Enablement"            # "Infrastructure and International"
  ServiceOrg: "Platform Services"                   # "Enterprise Container Services"
  ServiceProgram: "Galaxy Services"                 # "Cloud Service"
  ServiceName: "OpenShift @ AWS"                    # "OpenShift Container Platform"
  ServiceId: "ocp"                                  # ocp
# AccountId:	************
  RegionId: ue1
  Environment: sbx
  CommonId: "*********"
