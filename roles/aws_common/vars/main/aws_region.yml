---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        aws_region                                                        #
# Version:                                                                        #
#               2024-01-24 WRC. Initial                                           #
# Create Date:  2024-01-23                                                        #
# <AUTHOR> <EMAIL>                #
# Description:                                                                    #
#               This is the variable file that contains the region data that may  #
#               be passed into this module.                                       #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

# ------------------------------------------------------------------------------- #
# AWS Account information                                                         #
# ------------------------------------------------------------------------------- #
aws_account:

  # ----------------------- #
  # ACCOUNT Common          #
  # ----------------------- #
  id: ************
  name: inimcmprdp
  business_area: Space
  business_area_id: space
  business_org: "Information Technology & Digital Enablement"   # Organization under the business_area
  service_org: "Platform Services"                              # Organization under the business_org
  service_program: Galaxy                                       # Program under the service_org
  service_program_id: glxy                                       # Program under the service_org
  service_name: Galaxy Services
  service_id: glxy

# ------------------------------------------------------------------------------- #
# AWS Region Map translates current region name to short ID                       #
# ------------------------------------------------------------------------------- #
# aws_region_ids:
  # us-gov-east-1: "uge1"
  # us-gov-west-1: "ugw1"
  # us-east-1: "upe1"
  # us-west-1: "upw1"

# ------------------------------------------------------------------------------- #
# AWS Region Map translates current region name to short ID                       #
# ------------------------------------------------------------------------------- #
aws_region:
  id: upe1
  name: us-east-1
  partition: aws
  domain: "us.lmco.com"
  az_list: ["us-east-1a", "us-east-1b"]
  cidr_public: ["166.28.137.0/27", "166.28.137.32/27"]
  tgw: null
  default_rtb: "rtb-"       #  VPC Default/Main route table automatically created with VPC

# ------------------------------------------------------------------------------- #
# AWS Environment to specify what to build                                        #
# ------------------------------------------------------------------------------- #
aws_environment:
  id: global
  name: global environment

# aws_environment:
  # id: global
  # name: global environment

  # acount_id: ************
  # region_id: upe1
  # environment_id: globl
  #                 snd00
