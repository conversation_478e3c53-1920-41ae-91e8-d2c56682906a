---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        aws_credentials                                                   #
# Version:                                                                        #
#               2024-01-24 WRC. Initial                                           #
# Create Date:  2024-01-23                                                        #
# <AUTHOR> <EMAIL>                #
# Description:                                                                    #
#               This is the variable file that contains the credentials data      #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

# ------------------------------------------------------------------------------- #
# AWS Account information                                                         #
# ------------------------------------------------------------------------------- #
aws_common_credentials:
  init:
    name: Intialization Key
    description: AWS Key used as initiatialization time and potentially derived from environment variables.
    access_key_id: "{{ lookup('ansible.builtin.env', 'AWS_ACCESS_KEY_ID', default='********************') }}"
    access_key_secret: "{{ lookup('ansible.builtin.env', 'AWS_SECRET_ACCESS_KEY', default='RsvSii1lfSqe/8v06zRkkIucJyq0LTGNgDWOSdJA') }}"
    default_region: "{{ lookup('ansible.builtin.env', 'AWS_DEFAULT_REGION', default='us-east-1') }}"
  tfstate:
    name: Terraform State Key
    description: AWS Key used to access Terraform state data.
    access_key_id: none
    access_key_secret: none
    default_region: "{{ lookup('ansible.builtin.env', 'AWS_DEFAULT_REGION', default='us-east-1') }}"
  aws:
    name: Terraform AWS Resources Key
    description: AWS Key used to access all AWS resources that are part of the automation.
    access_key_id: none
    access_key_secret: none
    default_region: "{{ lookup('ansible.builtin.env', 'AWS_DEFAULT_REGION', default='us-east-1') }}"
  openshift:
    name: Terraform OpenShift Resources Key
    description: AWS Key used to access all OpenShift resources that are part of the automation.
    access_key_id: none
    access_key_secret: none
    default_region: "{{ lookup('ansible.builtin.env', 'AWS_DEFAULT_REGION', default='us-east-1') }}"
