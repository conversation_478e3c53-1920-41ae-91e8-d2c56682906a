---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        tasks/main.yml                                                    #
# Version:                                                                        #
#               2024-01-27 WRC. Galaxy Integration Update                         #
#               2024-02-07 WRC. Initial                                           #
# Create Date:  2024-02-07                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               Common tasks for all of the AWS collection roles.                 #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: "Tasks INPUT Variables for {{ role_name }}"
  vars:
    inputs:
      aws_common_state: "{{ aws_common_state | default(None) }}"
      aws_common_config: "{{ aws_common_config | default(None) }}"
      aws_common_default: "{{ aws_common_default | default(None) }}"
      aws_common_vars: "{{ aws_common_vars | default(None) }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: 2

# ------------------------------------------------------------------------------- #
- name: Verify the AWS Common Variables not covered by argument_spec
  ansible.builtin.assert:
    quiet: false
    success_msg: Parameter validation was SUCCESSFUL
    fail_msg: Galaxy Parameter validation FAILED
    that:
      - aws_common_vars.aws_access_key_id | length == 20
      - aws_common_vars.aws_secret_access_key | length == 40
      - aws_common_vars.max_attempts | int == 50
      - aws_common_vars.aws_default_output == "json"
      - aws_common_vars.aws_default_region in ["us-east-1","us-west-1","us-gov-east-1","us-gov-west-1"]

# ------------------------------------------------------------------------------- #
- name: Get AWS information based on the provided access key
  amazon.aws.aws_caller_info:
    region: "{{ aws_common_vars.aws_default_region }}"
    access_key: "{{ aws_common_vars.aws_access_key_id }}"
    secret_key: "{{ aws_common_vars.aws_secret_access_key }}"
    validate_certs: "{{ aws_common_vars.validate_certs }}"

  # ----------------------- #
  # Standard Attributes     #
  # ----------------------- #
  register: aws_caller_info

# ------------------------------------------------------------------------------- #
- name: Create the relevant facts from tasks here
  vars:
    regions_map:
      us-east-1: "upe1"
      us-west-1: "upw1"
      us-gov-east-1: "uge1"
      us-gov-west-1: "ugw1"
  ansible.builtin.set_fact:
    cacheable: false
    aws_common_facts:
      account: "{{ aws_caller_info.account }}"
      account_alias: "{{ aws_caller_info.account_alias }}"
      user_arn: "{{ aws_caller_info.arn }}"
      user_id: "{{ aws_caller_info.user_id }}"
      region_id: "{{ regions_map[aws_common_vars.aws_default_region] }}"
      partition: "{{ aws_caller_info.arn.split(':').1 }}" # Extract from ARN since no Ansible module

# ------------------------------------------------------------------------------- #
- name: Verify the AWS Common Facts
  ansible.builtin.assert:
    quiet: false
    success_msg: Galaxy Fact validation was SUCCESSFUL
    fail_msg: Galaxy Fact validation FAILED
    that:
      - aws_common_facts.account | length == 12
      - aws_common_facts.region_id | length == 4
      - aws_common_facts.partition | length >= 3

# ------------------------------------------------------------------------------- #
- name: "Tasks OUTPUT Variables for {{ role_name }}"
  vars:
    outputs:
      aws_caller_info: "{{ aws_caller_info | default(None) }}"
      aws_common_defaults: "{{ aws_common_defaults | default(None) }}"
      aws_common_config: "{{ aws_common_config | default(None) }}"
      aws_common_vars: "{{ aws_common_vars | default(None) }}"
      aws_common_facts: "{{ aws_common_facts | default(None) }}"
      aws_prefix_global: "{{ aws_prefix_global | default(None) }}"
  ansible.builtin.debug:
    var: outputs
    verbosity: 2
