---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        defaults/main/aws_tfvpc_defaults.yml                              #
# Version:                                                                        #
#               2024-10-05 Initial creation for VPC role                          #
# Create Date:  2024-10-05                                                        #
# Description:                                                                    #
#               Default values for the AWS VPC Terraform role                     #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

# ------------------------------------------- #
# Default VPC Configuration                  #
# ------------------------------------------- #
aws_tfvpc_defaults:
  # VPC Settings
  vpc_init: true
  vpc_cidr_block: "10.0.0.0/16"
  enable_dns_support: true
  enable_dns_hostnames: true
  
  # Subnet Settings
  public_subnet_cidr: "********/24"
  availability_zone: ""  # Will use first available AZ if empty
  map_public_ip_on_launch: true
  
  # Naming
  vpc_name_override: ""
  subnet_name_override: ""
  
  # Additional tags
  additional_tags: {}
  
  # Terraform Settings
  terraform_init: true
  terraform_plan: true
  terraform_apply: true
  terraform_destroy: false
  
  # Working Directory
  working_directory: "/tmp"
  
  # Module Path
  module_path: "terraform/modules/vpc"
  
  # State Management
  use_remote_state: false
  state_bucket: ""
  state_key: "vpc/terraform.tfstate"
  state_region: "us-east-1"
  
  # Standard Options
  no_log: false
  check_mode: false
