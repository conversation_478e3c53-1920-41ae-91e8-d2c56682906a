# AWS Terraform VPC Role (aws_tfvpc)

This Ansible role deploys AWS VPC infrastructure using Terraform modules. It creates a complete VPC setup including VPC, public subnet, internet gateway, and routing configuration.

## Features

- **VPC Creation**: Creates a VPC with configurable CIDR block
- **Public Subnet**: Creates a public subnet with internet access
- **Internet Gateway**: Provides internet connectivity
- **Route Tables**: Configures routing for public subnet
- **DNS Support**: Enables DNS resolution and hostnames
- **Flexible Configuration**: Highly configurable through variables
- **Terraform Integration**: Uses Terraform for infrastructure management
- **State Management**: Supports both local and remote state backends
- **Tagging**: Comprehensive resource tagging support

## Requirements

- Ansible >= 2.9
- Terraform >= 1.0
- AWS CLI configured with appropriate credentials
- Python boto3 library

## Role Variables

### Required Variables

None - all variables have sensible defaults.

### Optional Variables

The role accepts configuration through the `aws_tfvpc_config` dictionary:

```yaml
aws_tfvpc_config:
  # AWS Configuration
  account: "************"               # AWS account ID
  region: "us-east-1"                   # AWS region
  aws_profile: "default"                # AWS CLI profile

  # Environment Configuration
  environment: "dev"                    # Environment name

  # VPC Configuration
  vpc_init: true                        # Whether to create VPC resources
  vpc_cidr_block: "10.0.0.0/16"        # VPC CIDR block
  enable_dns_support: true              # Enable DNS support
  enable_dns_hostnames: true            # Enable DNS hostnames

  # Subnet Configuration
  public_subnet_cidr: "********/24"    # Public subnet CIDR
  availability_zone: ""                 # AZ (empty for auto-select)
  map_public_ip_on_launch: true         # Auto-assign public IPs

  # Tagging
  additional_tags:                      # Additional resource tags
    Project: "MyProject"
    Owner: "DevOps"

  # Terraform Control
  terraform_init: true                  # Run terraform init
  terraform_plan: true                  # Run terraform plan
  terraform_apply: true                 # Run terraform apply
  terraform_destroy: false              # Run terraform destroy

  # Cleanup
  cleanup_temp_files: true              # Clean temporary files
```

## Dependencies

This role is completely self-contained and has no dependencies on other roles.

## Example Playbook

### Basic Usage

```yaml
- hosts: localhost
  vars:
    aws_tfvpc_config:
      account: "************"
      region: "us-east-1"
      environment: "dev"
      vpc_cidr_block: "10.0.0.0/16"
      public_subnet_cidr: "********/24"

  tasks:
    - name: Deploy VPC
      ansible.builtin.include_role:
        name: aws_tfvpc
```

### Advanced Usage with Remote State

```yaml
- hosts: localhost
  vars:
    aws_tfvpc_config:
      account: "************"
      region: "us-west-2"
      environment: "prod"
      vpc_cidr_block: "**********/16"
      public_subnet_cidr: "**********/24"
      availability_zone: "us-west-2a"

      # Remote state configuration
      use_remote_state: true
      state_bucket: "my-terraform-state"
      state_key: "vpc/prod/terraform.tfstate"
      state_region: "us-west-2"

      # Additional tags
      additional_tags:
        Environment: "production"
        Project: "WebApp"
        CostCenter: "Engineering"

  tasks:
    - name: Deploy VPC
      ansible.builtin.include_role:
        name: aws_tfvpc
```

## Outputs

The role provides the following outputs in `aws_tfvpc_outputs`:

- `vpc_id`: VPC ID
- `vpc_arn`: VPC ARN
- `vpc_cidr_block`: VPC CIDR block
- `public_subnet_id`: Public subnet ID
- `public_subnet_arn`: Public subnet ARN
- `public_subnet_cidr_block`: Public subnet CIDR
- `internet_gateway_id`: Internet Gateway ID
- `public_route_table_id`: Public route table ID
- `availability_zone`: Subnet availability zone

## File Structure

```text
roles/aws_tfvpc/
├── README.md
├── defaults/
│   └── main/
│       └── aws_tfvpc_defaults.yml
├── meta/
│   ├── argument_specs.yml
│   └── main.yml
├── tasks/
│   ├── main.yml
│   ├── aws_tfvpc_build.yml
│   ├── aws_tfvpc_plan.yml
│   ├── aws_tfvpc_apply.yml
│   ├── aws_tfvpc_destroy.yml
│   └── aws_tfvpc_cleanup.yml
├── templates/
│   ├── main.tf.j2
│   ├── providers.tf.j2
│   ├── terraform.tf.j2
│   ├── variables.tf.j2
│   └── outputs.tf.j2
└── vars/
    └── main/
        └── aws_tfvpc_vars.yml
```

## Terraform Module

The role uses the VPC Terraform module located at `terraform/modules/vpc/` which includes:

- `vpc_main.tf`: Main VPC resources
- `variables.tf`: Variable definitions
- `outputs.tf`: Output definitions
- `providers.tf`: Provider configuration

## Authors

This role is maintained by the Galaxy Team. Updated for comprehensive VPC infrastructure deployment.

## License

License terms are defined at the collection level in the [LICENSE](../../LICENSE) file.
