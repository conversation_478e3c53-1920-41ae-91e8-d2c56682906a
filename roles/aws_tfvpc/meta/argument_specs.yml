---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        argument_specs.yml                                                #
# Version:                                                                        #
#               2024-10-05 Updated for VPC role implementation                    #
#               2024-01-24 WRC. Initial                                           #
# Create Date:  2024-01-24                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               This is the argument specification for the aws_tfvpc role. With   #
#               this file present, a new task is inserted at the beginning of     #
#               role execution that will validate the parameters supplied for     #
#               the role against the specification. If the parameters fail        #
#               validation, the role will fail execution.                        #
# Inputs:                                                                         #
#               aws_tfvpc_config - Configuration dictionary for VPC deployment    #
# Outputs:                                                                        #
#               aws_tfvpc_outputs - VPC deployment outputs                        #
#                                                                                 #
# ------------------------------------------------------------------------------- #
# Reference:                                                                      #
# https://docs.ansible.com/ansible/latest/collections/ansible/builtin/validate_argument_spec_module.html
argument_specs:
  main:
    author: Casagrande, Wayne (US) <<EMAIL>>
    short_description: Galaxy Amazon Web Services Terraform VPC role entrypoint
    description: |
      Galaxy Amazon Web Services main entry point for the Terraform VPC role.
      This role deploys VPC infrastructure including VPC, subnets, internet gateway,
      and routing using Terraform modules.

    # ----------------------------------------------- #
    # Options are “parameters” or “variables”.        #
    # ----------------------------------------------- #
    options:
      aws_tfvpc_config:
        description: Configuration dictionary for VPC deployment
        type: dict
        required: false
        default: {}
        options:
          account:
            description: AWS account ID
            type: str
            required: false
            default: "************"

          region:
            description: AWS region
            type: str
            required: false
            default: "us-east-1"

          environment:
            description: Environment name (e.g., dev, staging, prod)
            type: str
            required: false
            default: "dev"

          aws_profile:
            description: AWS CLI profile to use
            type: str
            required: false
            default: "default"

          vpc_init:
            description: Whether to initialize VPC resources
            type: bool
            required: false
            default: true

          vpc_cidr_block:
            description: CIDR block for the VPC
            type: str
            required: false
            default: "10.0.0.0/16"

          public_subnet_cidr:
            description: CIDR block for the public subnet
            type: str
            required: false
            default: "********/24"

          availability_zone:
            description: Availability zone for the subnet (empty for auto-select)
            type: str
            required: false
            default: ""

          enable_dns_support:
            description: Enable DNS support in the VPC
            type: bool
            required: false
            default: true

          enable_dns_hostnames:
            description: Enable DNS hostnames in the VPC
            type: bool
            required: false
            default: true

          map_public_ip_on_launch:
            description: Map public IP on launch for public subnet
            type: bool
            required: false
            default: true

          vpc_name_override:
            description: Override for VPC name
            type: str
            required: false
            default: ""

          subnet_name_override:
            description: Override for subnet name
            type: str
            required: false
            default: ""

          additional_tags:
            description: Additional tags to apply to all resources
            type: dict
            required: false
            default: {}

          terraform_init:
            description: Whether to run terraform init
            type: bool
            required: false
            default: true

          terraform_plan:
            description: Whether to run terraform plan
            type: bool
            required: false
            default: true

          terraform_apply:
            description: Whether to run terraform apply
            type: bool
            required: false
            default: true

          terraform_destroy:
            description: Whether to run terraform destroy
            type: bool
            required: false
            default: false

          use_remote_state:
            description: Whether to use remote state backend
            type: bool
            required: false
            default: false

          state_bucket:
            description: S3 bucket for remote state
            type: str
            required: false
            default: ""

          state_key:
            description: S3 key for remote state
            type: str
            required: false
            default: "vpc/terraform.tfstate"

          state_region:
            description: AWS region for remote state
            type: str
            required: false
            default: "us-east-1"

          cleanup_temp_files:
            description: Whether to cleanup temporary files after execution
            type: bool
            required: false
            default: true
