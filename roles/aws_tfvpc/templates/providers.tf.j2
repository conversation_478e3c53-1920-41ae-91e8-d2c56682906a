#############################################################################################
#                                                                                           #
# Title:        providers.tf                                                                #
# Version:                                                                                  #
#               {{ aws_tfvpc_timestamp.date }} Generated by Ansible aws_tfvpc role         #
# Create Date:  {{ aws_tfvpc_timestamp.date }}                                             #
# Description:                                                                              #
#               Provider configuration for VPC deployment                                  #
#               Generated from Ansible template                                            #
#                                                                                           #
#############################################################################################

terraform {
  required_version = ">= 1.0"
  
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.33"
    }
  }

{% if aws_tfvpc_backend_config.use_remote_state %}
  backend "s3" {
    bucket         = "{{ aws_tfvpc_backend_config.bucket }}"
    key            = "{{ aws_tfvpc_backend_config.key }}"
    region         = "{{ aws_tfvpc_backend_config.region }}"
    encrypt        = {{ aws_tfvpc_backend_config.encrypt | lower }}
{% if aws_tfvpc_backend_config.dynamodb_table %}
    dynamodb_table = "{{ aws_tfvpc_backend_config.dynamodb_table }}"
{% endif %}
  }
{% endif %}
}

provider "aws" {
  region = "{{ aws_tfvpc_terraform_vars.region }}"
  
{% if aws_common_credentials.profile is defined %}
  profile = "{{ aws_common_credentials.profile }}"
{% endif %}

{% if aws_common_credentials.shared_credentials_files is defined %}
  shared_credentials_files = {{ aws_common_credentials.shared_credentials_files | to_json }}
{% endif %}

{% if aws_common_credentials.allowed_account_ids is defined %}
  allowed_account_ids = {{ aws_common_credentials.allowed_account_ids | to_json }}
{% endif %}

  max_retries = {{ aws_common_credentials.max_retries | default(50) }}
  insecure    = {{ aws_common_credentials.insecure | default(false) | lower }}

  default_tags {
    tags = {
      Environment = "{{ aws_tfvpc_terraform_vars.environment }}"
      ManagedBy   = "Terraform"
      CreatedBy   = "Ansible-aws_tfvpc"
      Timestamp   = "{{ aws_tfvpc_timestamp.iso8601 }}"
    }
  }
}
