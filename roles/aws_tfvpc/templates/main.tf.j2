#############################################################################################
#                                                                                           #
# Title:        main.tf                                                                     #
# Version:                                                                                  #
#               {{ aws_tfvpc_timestamp.date }} Generated by Ansible aws_tfvpc role         #
# Create Date:  {{ aws_tfvpc_timestamp.date }}                                             #
# Description:                                                                              #
#               Main Terraform configuration for VPC deployment                            #
#               Generated from Ansible template                                            #
#                                                                                           #
#############################################################################################

# ------------------------------------------------------------------------------- #
# VPC Module Call                                                                 #
# ------------------------------------------------------------------------------- #
module "vpc" {
  source = "./modules/vpc"

  # Required Variables
  account     = "{{ aws_tfvpc_terraform_vars.account }}"
  region      = "{{ aws_tfvpc_terraform_vars.region }}"
  environment = "{{ aws_tfvpc_terraform_vars.environment }}"
  common_data = {
    tags = {
{% for key, value in aws_tfvpc_terraform_vars.additional_tags.items() %}
      {{ key }} = "{{ value }}"
{% endfor %}
      Environment = "{{ aws_tfvpc_terraform_vars.environment }}"
      ManagedBy   = "Terraform"
      CreatedBy   = "Ansible-aws_tfvpc"
      Timestamp   = "{{ aws_tfvpc_timestamp.iso8601 }}"
    }
  }

  # VPC Configuration
  vpc_init                = {{ aws_tfvpc_terraform_vars.vpc_init | lower }}
  vpc_cidr_block         = "{{ aws_tfvpc_terraform_vars.vpc_cidr_block }}"
  enable_dns_support     = {{ aws_tfvpc_terraform_vars.enable_dns_support | lower }}
  enable_dns_hostnames   = {{ aws_tfvpc_terraform_vars.enable_dns_hostnames | lower }}

  # Subnet Configuration
  public_subnet_cidr      = "{{ aws_tfvpc_terraform_vars.public_subnet_cidr }}"
  availability_zone       = "{{ aws_tfvpc_terraform_vars.availability_zone }}"
  map_public_ip_on_launch = {{ aws_tfvpc_terraform_vars.map_public_ip_on_launch | lower }}

  # Optional Overrides
{% if aws_tfvpc_terraform_vars.vpc_name_override %}
  vpc_name_override       = "{{ aws_tfvpc_terraform_vars.vpc_name_override }}"
{% endif %}
{% if aws_tfvpc_terraform_vars.subnet_name_override %}
  subnet_name_override    = "{{ aws_tfvpc_terraform_vars.subnet_name_override }}"
{% endif %}
}

# ------------------------------------------------------------------------------- #
# Outputs                                                                         #
# ------------------------------------------------------------------------------- #
output "vpc_id" {
  description = "ID of the VPC"
  value       = module.vpc.vpc_id
}

output "vpc_arn" {
  description = "ARN of the VPC"
  value       = module.vpc.vpc_arn
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = module.vpc.vpc_cidr_block
}

output "public_subnet_id" {
  description = "ID of the public subnet"
  value       = module.vpc.public_subnet_id
}

output "public_subnet_arn" {
  description = "ARN of the public subnet"
  value       = module.vpc.public_subnet_arn
}

output "public_subnet_cidr_block" {
  description = "CIDR block of the public subnet"
  value       = module.vpc.public_subnet_cidr_block
}

output "internet_gateway_id" {
  description = "ID of the Internet Gateway"
  value       = module.vpc.internet_gateway_id
}

output "public_route_table_id" {
  description = "ID of the public route table"
  value       = module.vpc.public_route_table_id
}

output "availability_zone" {
  description = "Availability zone of the public subnet"
  value       = module.vpc.public_subnet_availability_zone
}

output "vpc_data" {
  description = "Complete VPC data object"
  value       = module.vpc.vpc_data
}
