#############################################################################################
#                                                                                           #
# Title:        terraform.tf                                                                #
# Version:                                                                                  #
#               {{ aws_tfvpc_timestamp.date }} Generated by Ansible aws_tfvpc role         #
# Create Date:  {{ aws_tfvpc_timestamp.date }}                                             #
# Description:                                                                              #
#               Terraform configuration and version constraints                            #
#               Generated from Ansible template                                            #
#                                                                                           #
#############################################################################################

# This file is generated by the aws_tfvpc Ansible role
# Configuration timestamp: {{ aws_tfvpc_timestamp.iso8601 }}
# Environment: {{ aws_tfvpc_terraform_vars.environment }}
# Region: {{ aws_tfvpc_terraform_vars.region }}
# Account: {{ aws_tfvpc_terraform_vars.account }}

# Terraform version and provider requirements are defined in providers.tf
