---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        vars/main/aws_tfvpc_vars.yml                                      #
# Version:                                                                        #
#               2024-10-05 Initial creation for VPC role                          #
# Create Date:  2024-10-05                                                        #
# Description:                                                                    #
#               Variable definitions and merging for the AWS VPC role             #
# Inputs:                                                                         #
#               aws_tfvpc_config - Configuration dictionary from user             #
# Outputs:                                                                        #
#               aws_tfvpc_vars - Merged configuration variables                   #
#                                                                                 #
# ------------------------------------------------------------------------------- #

# ------------------------------------------------------------------------------- #
# Get current time so that the modules have a single time snapshot                #
# ------------------------------------------------------------------------------- #
aws_tfvpc_timestamp: "{{ ansible_date_time }}"

# ------------------------------------------------------------------------------- #
# VPC Prefix for resource naming                                                  #
# ------------------------------------------------------------------------------- #
aws_tfvpc_prefix: "{{ aws_common_facts.region_id | default('ue1') }}-{{ aws_tfvpc_vars.environment | default('dev') }}"
aws_tfvpc_prefix_global: "{{ aws_common_facts.account | default('************') }}-{{ aws_tfvpc_prefix }}"

# ------------------------------------------------------------------------------- #
# AWS VPC Variables from defaults and inbound configuration overrides            #
# ------------------------------------------------------------------------------- #
aws_tfvpc_vars: "{{ aws_tfvpc_defaults | ansible.builtin.combine(aws_tfvpc_config | default({}), recursive=true) }}"

# ------------------------------------------------------------------------------- #
# Terraform Variables for VPC Module                                             #
# ------------------------------------------------------------------------------- #
aws_tfvpc_terraform_vars:
  account: "{{ aws_common_facts.account | default('************') }}"
  region: "{{ aws_common_facts.region | default('us-east-1') }}"
  environment: "{{ aws_tfvpc_vars.environment | default('dev') }}"
  vpc_init: "{{ aws_tfvpc_vars.vpc_init | default(true) }}"
  vpc_cidr_block: "{{ aws_tfvpc_vars.vpc_cidr_block | default('10.0.0.0/16') }}"
  public_subnet_cidr: "{{ aws_tfvpc_vars.public_subnet_cidr | default('********/24') }}"
  availability_zone: "{{ aws_tfvpc_vars.availability_zone | default('') }}"
  enable_dns_support: "{{ aws_tfvpc_vars.enable_dns_support | default(true) }}"
  enable_dns_hostnames: "{{ aws_tfvpc_vars.enable_dns_hostnames | default(true) }}"
  map_public_ip_on_launch: "{{ aws_tfvpc_vars.map_public_ip_on_launch | default(true) }}"
  vpc_name_override: "{{ aws_tfvpc_vars.vpc_name_override | default('') }}"
  subnet_name_override: "{{ aws_tfvpc_vars.subnet_name_override | default('') }}"
  additional_tags: "{{ aws_tfvpc_vars.additional_tags | default({}) }}"
  common_data: "{{ aws_common_data | default({}) }}"

# ------------------------------------------------------------------------------- #
# Terraform Backend Configuration                                                 #
# ------------------------------------------------------------------------------- #
aws_tfvpc_backend_config:
  use_remote_state: "{{ aws_tfvpc_vars.use_remote_state | default(false) }}"
  bucket: "{{ aws_tfvpc_vars.state_bucket | default('') }}"
  key: "{{ aws_tfvpc_vars.state_key | default('vpc/terraform.tfstate') }}"
  region: "{{ aws_tfvpc_vars.state_region | default('us-east-1') }}"
  encrypt: true
  dynamodb_table: "{{ aws_tfvpc_vars.state_dynamodb_table | default('') }}"
