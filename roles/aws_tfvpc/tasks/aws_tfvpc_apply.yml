---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        tasks/aws_tfvpc_apply.yml                                         #
# Version:                                                                        #
#               2024-10-05 Initial creation for VPC role                          #
# Create Date:  2024-10-05                                                        #
# Description:                                                                    #
#               Execute Terraform apply for VPC infrastructure                    #
# Inputs:                                                                         #
#               aws_tfvpc_tempdir - Temporary directory with Terraform files      #
# Outputs:                                                                        #
#               aws_tfvpc_apply_result - Terra<PERSON> apply execution results        #
#               aws_tfvpc_outputs - VPC infrastructure outputs                    #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_entry.name }}"
  vars:
    inputs:
      aws_tfvpc_tempdir: "{{ aws_tfvpc_tempdir }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: "{{ ansible_common_debug.task_entry.verbosity }}"

# ------------------------------------------------------------------------------- #
- name: Execute Terraform apply
  ansible.builtin.command:
    cmd: terraform apply -auto-approve tfplan
    chdir: "{{ aws_tfvpc_tempdir.path }}"
  register: aws_tfvpc_apply_result
  no_log: false
  check_mode: false

# ------------------------------------------------------------------------------- #
- name: Display Terraform apply results
  ansible.builtin.debug:
    msg: |
      Terraform Apply Results:
      {{ aws_tfvpc_apply_result.stdout }}
    verbosity: 1

# ------------------------------------------------------------------------------- #
- name: Display Terraform apply errors (if any)
  ansible.builtin.debug:
    msg: |
      Terraform Apply Errors:
      {{ aws_tfvpc_apply_result.stderr }}
    verbosity: 1
  when: aws_tfvpc_apply_result.stderr | length > 0

# ------------------------------------------------------------------------------- #
- name: Fail if Terraform apply failed
  ansible.builtin.fail:
    msg: "Terraform apply failed with return code {{ aws_tfvpc_apply_result.rc }}"
  when: aws_tfvpc_apply_result.rc != 0

# ------------------------------------------------------------------------------- #
- name: Get Terraform outputs
  ansible.builtin.command:
    cmd: terraform output -json
    chdir: "{{ aws_tfvpc_tempdir.path }}"
  register: aws_tfvpc_terraform_outputs
  no_log: false
  check_mode: false

# ------------------------------------------------------------------------------- #
- name: Parse Terraform outputs
  ansible.builtin.set_fact:
    aws_tfvpc_outputs: "{{ aws_tfvpc_terraform_outputs.stdout | from_json }}"
  when: aws_tfvpc_terraform_outputs.stdout | length > 0

# ------------------------------------------------------------------------------- #
- name: Display VPC outputs
  ansible.builtin.debug:
    var: aws_tfvpc_outputs
    verbosity: 1

# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_exit.name }}"
  vars:
    outputs:
      aws_tfvpc_apply_result: "{{ aws_tfvpc_apply_result }}"
      aws_tfvpc_outputs: "{{ aws_tfvpc_outputs | default({}) }}"
  ansible.builtin.debug:
    var: outputs
    verbosity: "{{ ansible_common_debug.task_exit.verbosity }}"
