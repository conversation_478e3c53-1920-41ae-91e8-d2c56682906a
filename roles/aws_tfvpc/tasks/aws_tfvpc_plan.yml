---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        tasks/aws_tfvpc_plan.yml                                          #
# Version:                                                                        #
#               2024-10-05 Initial creation for VPC role                          #
# Create Date:  2024-10-05                                                        #
# Description:                                                                    #
#               Execute Terraform plan for VPC infrastructure                     #
# Inputs:                                                                         #
#               aws_tfvpc_tempdir - Temporary directory with Terraform files      #
# Outputs:                                                                        #
#               aws_tfvpc_plan_result - Terraform plan execution results          #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_entry.name }}"
  vars:
    inputs:
      aws_tfvpc_tempdir: "{{ aws_tfvpc_tempdir }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: "{{ ansible_common_debug.task_entry.verbosity }}"

# ------------------------------------------------------------------------------- #
- name: Execute Terraform plan
  ansible.builtin.command:
    cmd: terraform plan -out=tfplan
    chdir: "{{ aws_tfvpc_tempdir.path }}"
  register: aws_tfvpc_plan_result
  no_log: false
  check_mode: false

# ------------------------------------------------------------------------------- #
- name: Display Terraform plan results
  ansible.builtin.debug:
    msg: |
      Terraform Plan Results:
      {{ aws_tfvpc_plan_result.stdout }}
    verbosity: 1

# ------------------------------------------------------------------------------- #
- name: Display Terraform plan errors (if any)
  ansible.builtin.debug:
    msg: |
      Terraform Plan Errors:
      {{ aws_tfvpc_plan_result.stderr }}
    verbosity: 1
  when: aws_tfvpc_plan_result.stderr | length > 0

# ------------------------------------------------------------------------------- #
- name: Fail if Terraform plan failed
  ansible.builtin.fail:
    msg: "Terraform plan failed with return code {{ aws_tfvpc_plan_result.rc }}"
  when: aws_tfvpc_plan_result.rc != 0

# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_exit.name }}"
  vars:
    outputs:
      aws_tfvpc_plan_result: "{{ aws_tfvpc_plan_result }}"
  ansible.builtin.debug:
    var: outputs
    verbosity: "{{ ansible_common_debug.task_exit.verbosity }}"
