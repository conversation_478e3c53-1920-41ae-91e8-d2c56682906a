---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        tasks/main.yml                                                    #
# Version:                                                                        #
#               2024-10-05 Initial creation for VPC role                          #
# Create Date:  2024-10-05                                                        #
# Description:                                                                    #
#               Ansible main entry point for the aws_tfvpc role                   #
# Inputs:                                                                         #
#               aws_tfvpc_config - Configuration dictionary                       #
# Outputs:                                                                        #
#               aws_tfvpc_outputs - VPC deployment outputs                        #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_entry.name }}"
  vars:
    inputs:
      aws_common_credentials: "{{ aws_common_credentials | default({}) }}"
      aws_tfvpc_config: "{{ aws_tfvpc_config | default({}) }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: "{{ ansible_common_debug.task_entry.verbosity }}"

# ------------------------------------------------------------------------------- #
- name: Execute aws_tfvpc_build task
  ansible.builtin.include_tasks: aws_tfvpc_build.yml

- name: Execute aws_tfvpc_plan task
  ansible.builtin.include_tasks: aws_tfvpc_plan.yml
  when: aws_tfvpc_vars.terraform_plan | default(true)

- name: Execute aws_tfvpc_apply task
  ansible.builtin.include_tasks: aws_tfvpc_apply.yml
  when: aws_tfvpc_vars.terraform_apply | default(true)

- name: Execute aws_tfvpc_destroy task
  ansible.builtin.include_tasks: aws_tfvpc_destroy.yml
  when: aws_tfvpc_vars.terraform_destroy | default(false)

- name: Execute aws_tfvpc_cleanup task
  ansible.builtin.include_tasks: aws_tfvpc_cleanup.yml

# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_exit.name }}"
  vars:
    outputs:
      aws_common_credentials: "{{ aws_common_credentials | default({}) }}"
      aws_tfvpc_tempdir: "{{ aws_tfvpc_tempdir | default({}) }}"
      aws_tfvpc_outputs: "{{ aws_tfvpc_outputs | default({}) }}"
  ansible.builtin.debug:
    var: outputs
    verbosity: "{{ ansible_common_debug.task_exit.verbosity }}"
