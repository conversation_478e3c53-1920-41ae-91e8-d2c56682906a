---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        tasks/aws_tfvpc_destroy.yml                                       #
# Version:                                                                        #
#               2024-10-05 Initial creation for VPC role                          #
# Create Date:  2024-10-05                                                        #
# Description:                                                                    #
#               Execute Terraform destroy for VPC infrastructure                  #
# Inputs:                                                                         #
#               aws_tfvpc_tempdir - Temporary directory with Terraform files      #
# Outputs:                                                                        #
#               aws_tfvpc_destroy_result - Terraform destroy execution results    #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_entry.name }}"
  vars:
    inputs:
      aws_tfvpc_tempdir: "{{ aws_tfvpc_tempdir }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: "{{ ansible_common_debug.task_entry.verbosity }}"

# ------------------------------------------------------------------------------- #
- name: Confirm destruction (safety check)
  ansible.builtin.pause:
    prompt: "Are you sure you want to destroy the VPC infrastructure? Type 'yes' to continue"
  register: destroy_confirmation
  when: aws_tfvpc_vars.terraform_destroy | default(false)

# ------------------------------------------------------------------------------- #
- name: Execute Terraform destroy
  ansible.builtin.command:
    cmd: terraform destroy -auto-approve
    chdir: "{{ aws_tfvpc_tempdir.path }}"
  register: aws_tfvpc_destroy_result
  when: 
    - aws_tfvpc_vars.terraform_destroy | default(false)
    - destroy_confirmation.user_input | default('') == 'yes'
  no_log: false
  check_mode: false

# ------------------------------------------------------------------------------- #
- name: Display Terraform destroy results
  ansible.builtin.debug:
    msg: |
      Terraform Destroy Results:
      {{ aws_tfvpc_destroy_result.stdout }}
    verbosity: 1
  when: aws_tfvpc_destroy_result is defined

# ------------------------------------------------------------------------------- #
- name: Display Terraform destroy errors (if any)
  ansible.builtin.debug:
    msg: |
      Terraform Destroy Errors:
      {{ aws_tfvpc_destroy_result.stderr }}
    verbosity: 1
  when: 
    - aws_tfvpc_destroy_result is defined
    - aws_tfvpc_destroy_result.stderr | length > 0

# ------------------------------------------------------------------------------- #
- name: Fail if Terraform destroy failed
  ansible.builtin.fail:
    msg: "Terraform destroy failed with return code {{ aws_tfvpc_destroy_result.rc }}"
  when: 
    - aws_tfvpc_destroy_result is defined
    - aws_tfvpc_destroy_result.rc != 0

# ------------------------------------------------------------------------------- #
- name: Skip destruction message
  ansible.builtin.debug:
    msg: "VPC destruction was skipped (not confirmed or not requested)"
  when: 
    - aws_tfvpc_vars.terraform_destroy | default(false) == false or
      destroy_confirmation.user_input | default('') != 'yes'

# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_exit.name }}"
  vars:
    outputs:
      aws_tfvpc_destroy_result: "{{ aws_tfvpc_destroy_result | default({}) }}"
  ansible.builtin.debug:
    var: outputs
    verbosity: "{{ ansible_common_debug.task_exit.verbosity }}"
