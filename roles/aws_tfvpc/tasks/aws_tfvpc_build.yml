---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        tasks/aws_tfvpc_build.yml                                         #
# Version:                                                                        #
#               2024-10-05 Initial creation for VPC role                          #
# Create Date:  2024-10-05                                                        #
# Description:                                                                    #
#               Build Terraform configuration for VPC deployment                  #
# Inputs:                                                                         #
#               aws_tfvpc_vars - VPC configuration variables                      #
# Outputs:                                                                        #
#               aws_tfvpc_tempdir - Temporary directory for Terraform files       #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_entry.name }}"
  vars:
    inputs:
      aws_tfvpc_vars: "{{ aws_tfvpc_vars }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: "{{ ansible_common_debug.task_entry.verbosity }}"

# ------------------------------------------------------------------------------- #
- name: Create temporary Terraform VPC directory
  ansible.builtin.tempfile:
    state: directory
    prefix: "galaxy."
    suffix: ".tf_vpc"
  no_log: false
  register: aws_tfvpc_tempdir
  check_mode: false

# ------------------------------------------------------------------------------- #
- name: Create Terraform VPC files from templates
  ansible.builtin.template:
    src: "{{ role_path }}/templates/{{ item }}.j2"
    dest: "{{ aws_tfvpc_tempdir.path }}/{{ item }}"
    force: true
    mode: '0644'
  loop: 
    - main.tf
    - variables.tf
    - outputs.tf
    - providers.tf
    - terraform.tf
  no_log: false
  check_mode: false

# ------------------------------------------------------------------------------- #
- name: Copy VPC module files to temporary directory
  ansible.builtin.copy:
    src: "{{ playbook_dir }}/{{ aws_tfvpc_vars.module_path | default('terraform/modules/vpc') }}/"
    dest: "{{ aws_tfvpc_tempdir.path }}/modules/vpc/"
    mode: '0644'
  no_log: false
  check_mode: false

# ------------------------------------------------------------------------------- #
- name: Initialize Terraform
  ansible.builtin.command:
    cmd: terraform init
    chdir: "{{ aws_tfvpc_tempdir.path }}"
  register: aws_tfvpc_init_result
  when: aws_tfvpc_vars.terraform_init | default(true)
  no_log: false
  check_mode: false

# ------------------------------------------------------------------------------- #
- name: Display Terraform init results
  ansible.builtin.debug:
    var: aws_tfvpc_init_result
    verbosity: 1
  when: aws_tfvpc_init_result is defined

# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_exit.name }}"
  vars:
    outputs:
      aws_tfvpc_tempdir: "{{ aws_tfvpc_tempdir }}"
      aws_tfvpc_init_result: "{{ aws_tfvpc_init_result | default({}) }}"
  ansible.builtin.debug:
    var: outputs
    verbosity: "{{ ansible_common_debug.task_exit.verbosity }}"
