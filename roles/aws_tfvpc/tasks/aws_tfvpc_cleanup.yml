---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        tasks/aws_tfvpc_cleanup.yml                                       #
# Version:                                                                        #
#               2024-10-05 Initial creation for VPC role                          #
# Create Date:  2024-10-05                                                        #
# Description:                                                                    #
#               Cleanup temporary files and directories after VPC deployment      #
# Inputs:                                                                         #
#               aws_tfvpc_tempdir - Temporary directory to cleanup                #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_entry.name }}"
  vars:
    inputs:
      aws_tfvpc_tempdir: "{{ aws_tfvpc_tempdir | default({}) }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: "{{ ansible_common_debug.task_entry.verbosity }}"

# ------------------------------------------------------------------------------- #
- name: Remove temporary Terraform directory
  ansible.builtin.file:
    path: "{{ aws_tfvpc_tempdir.path }}"
    state: absent
  when: 
    - aws_tfvpc_tempdir is defined
    - aws_tfvpc_tempdir.path is defined
    - aws_tfvpc_vars.cleanup_temp_files | default(true)
  no_log: false
  check_mode: false

# ------------------------------------------------------------------------------- #
- name: Display cleanup status
  ansible.builtin.debug:
    msg: "Temporary directory {{ aws_tfvpc_tempdir.path | default('N/A') }} has been cleaned up"
  when: 
    - aws_tfvpc_tempdir is defined
    - aws_tfvpc_tempdir.path is defined
    - aws_tfvpc_vars.cleanup_temp_files | default(true)

# ------------------------------------------------------------------------------- #
- name: Skip cleanup message
  ansible.builtin.debug:
    msg: "Cleanup was skipped - temporary files preserved at {{ aws_tfvpc_tempdir.path | default('N/A') }}"
  when: 
    - aws_tfvpc_tempdir is defined
    - aws_tfvpc_tempdir.path is defined
    - aws_tfvpc_vars.cleanup_temp_files | default(true) == false

# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_exit.name }}"
  vars:
    outputs:
      cleanup_performed: "{{ aws_tfvpc_vars.cleanup_temp_files | default(true) }}"
      temp_directory: "{{ aws_tfvpc_tempdir.path | default('N/A') }}"
  ansible.builtin.debug:
    var: outputs
    verbosity: "{{ ansible_common_debug.task_exit.verbosity }}"
