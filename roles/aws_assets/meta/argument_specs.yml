---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        argument_specs.yml                                                #
# Version:                                                                        #
#               2024-02-28 Initial                                                #
# Create Date:  2024-02-28                                                        #
# Author:       <PERSON>, <PERSON> (<EMAIL>)                             #
# Description:                                                                    #
#               Initial commit for argument_specs.yml                             #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
argument_specs:
  main:
    short_description: Operator tests role for aws_assets
    description:
      - lmco.aws.aws_assets
    author:
      - <PERSON>, <PERSON> (US) <<EMAIL>>
    options:
      galaxy_role:
        type: dict
        description: The dictionary providing the tags that will be used to determine what actions to execute
        required: true
        options:
          name:
            type: str
            required: true
            choices:
              - lmco.aws.aws_assets
            description: The name of the role that is being executed.
          software_version:
            type: list
            required: false
            description: List of approved software versions.
            choices:
              - default
          tags:
            type: list
            required: true
            description: The name of the role that is being executed.
            choices:
              - assets
