# AWS Terraform Root (lmco.aws.aws_tfroot)

This role is used to manage the Terraform Root directory use to define the configuration and orchestrate the deployment of Terraform resources contained in lower level modules. This provides the ability to replace all of the previous standalone root modules per environment by having Ansible orchestrate and dynamically generate the root module and execute.

## Authors

This role is maintained by the [Lockheed Martin Galaxy Team](https://galaxy.pages.gitlab.global.lmco.com/documents/about/team/). Please see our [Feedback Page](https://galaxy.pages.gitlab.global.lmco.com/documents/feedback/) if you would like to pass along ideas or suggestions.

## Dependencies

Dependencies are defined at the collection level in the [Requirements](../../requirements.yml) configuration.

## Requirements

Ansible requirements are defined in the [Role Metadata](meta/main.yml) for this role.

## Parameters

Role variables are defined in the [Argument Specification](meta/argument_specs.yml) for this role and are listed by Entry Point.

## Example Playbook

This role contains a specific Entry Point that coordinates the execution of tasks. The will support invocation as part of a Galaxy bundle but may also be invoked directly using the following playbook definition.

```yaml
 tasks:
    - ansible.builtin.include_role:
        name: aws_tfroot
        tasks_from: aws_tfroot_main
```

## License

License terms are defined at the collection level in the [LICENSE](../../LICENSE) file.
