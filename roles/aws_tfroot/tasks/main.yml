---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        tasks/main.yml                                                    #
# Version:                                                                        #
#               2024-02-01 WRC. Galaxy Integration Updates                        #
#               2024-01-30 WRC. Initial                                           #
# Create Date:  2024-01-30                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               Ansible main entry point for the tfroot role.                     #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_entry.name }}"
  vars:
    inputs:
      aws_common_credentials: "{{ aws_common_credentials }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: "{{ ansible_common_debug.task_entry.verbosity }}"

# ------------------------------------------------------------------------------- #
- name: Execute aws_tfroot_build task
  ansible.builtin.include_tasks: "{{ item }}.yml"
  loop: [aws_tfroot_build, aws_tfroot_apply, aws_tfroot_cleanup]

# - name: Execute aws_tfroot_apply task
#   ansible.builtin.include_tasks: aws_tfroot_apply.yml

# - name: Execute aws_tfroot_cleanup task
#   ansible.builtin.include_tasks: aws_tfroot_cleanup.yml

# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_exit.name }}"
  vars:
    outputs:
      aws_common_credentials: "{{ aws_common_credentials }}"
      aws_tfroot_tempdir: "{{ aws_tfroot_tempdir }}"
  ansible.builtin.debug:
    var: outputs
    verbosity: "{{ ansible_common_debug.task_exit.verbosity }}"
