---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        tasks/aws_tfroot_cleanup.yml                                      #
# Version:                                                                        #
#               2024-02-01 WRC. Galaxy Integration Updates                        #
#               2024-01-30 WRC. Initial                                           #
# Create Date:  2024-01-30                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               Ansible main entry point for the tfroot role.                     #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_entry.name }}"
  vars:
    inputs:
      aws_tfroot_tempdir: "{{ aws_tfroot_tempdir }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: "{{ ansible_common_debug.task_entry.verbosity }}"

# ------------------------------------------------------------------------------- #
- name: Delete temporary Terraform root directory
  ansible.builtin.file:
    state: absent
    path: "{{ aws_tfroot_tempdir.path }}"
  when: aws_tfroot_tempdir.path is defined

  # ------------------- #
  # Supported Opetions  #
  # ------------------- #
  check_mode: false

# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_exit.name }}"
  vars:
    outputs:
      aws_tfroot_tempdir: "{{ aws_tfroot_tempdir }}"
  ansible.builtin.debug:
    var: outputs
    verbosity: "{{ ansible_common_debug.task_exit.verbosity }}"
