---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        tasks/aws_tfroot_build.yml                                        #
# Version:                                                                        #
#               2024-02-01 WRC. Galaxy Integration Updates                        #
#               2024-01-30 WRC. Initial                                           #
# Create Date:  2024-01-30                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               Ansible main entry point for the tfroot role.                     #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_entry.name }}"
  vars:
    inputs:
      aws_common_credentials: "{{ aws_common_credentials }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: "{{ ansible_common_debug.task_entry.verbosity }}"

# ------------------------------------------------------------------------------- #
- name: Create temporary Terraform root directory
  ansible.builtin.tempfile:
    # path: Use system default
    state: directory
    prefix: "galaxy."
    suffix: ".tf_root"

  # ------------------- #
  # Standard Opetions   #
  # ------------------- #
  no_log: false
  register: aws_tfroot_tempdir
  check_mode: false

# ------------------------------------------------------------------------------- #
- name: Create Terraform Root files from templates
  ansible.builtin.template:
    src: "{{ role_path }}/templates/{{ item }}.j2"
    dest: "{{ aws_tfroot_tempdir.path }}/{{ item }}"
    force: true
    # owner: bin
    # group: wheel
    mode: '0644'
  loop: [credentials, main.tf, providers.tf, terraform.tf]

  # ------------------- #
  # Standard Options    #
  # ------------------- #
  no_log: false
  check_mode: false

# ------------------------------------------------------------------------------- #
# - name: Get the credentials from the S3 bucket
#   amazon.aws.s3_object:
#     bucket: 732988479216-upe1-global-tfstate # "{{ tfstate_s3_bucket.name }}"
#     region: "{{ aws_region.name }}"
#     access_key: "********************"
#     secret_key: "RsvSii1lfSqe/8v06zRkkIucJyq0LTGNgDWOSdJA"
#     object: credentials.732988479216.upe1
#     dest: "{{ aws_tfroot_tempdir.path }}/credentials.732988479216.upe1"
#     mode: get
#     overwrite: always
#     retries: 3        # On recoverable failure, how many times to retry before actually failing.
#     validate_bucket_name: true

#   # ------------------- #
#   # Standard Options    #
#   # ------------------- #
#   no_log: false
#   register: aws_tfroot_s3_object_out
#   check_mode: false

# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_exit.name }}"
  vars:
    outputs:
      aws_common_credentials: "{{ aws_tfstate_kms_key }}"
      aws_tfroot_tempdir: "{{ aws_tfroot_tempdir }}"
  ansible.builtin.debug:
    var: outputs
    verbosity: "{{ ansible_common_debug.task_exit.verbosity }}"
