# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.tf.j2                                                        #
# Version:                                                                        #
#               2024-01-25 WRC. Update for Galaxy Ansible                         #
#               2021-08-10 WRC. Initial                                           #
# Create Date:  2021-08-10                                                        #
# Author:       <PERSON><PERSON><PERSON>, Wayne (US) <<EMAIL>>                #
# Description:                                                                    #
#               Established the terraform state this account and environment      #
#                                                                                 #
# ------------------------------------------------------------------------------- #

#-----------------------------------------------#
# Variable definition                           #
#-----------------------------------------------#
variable "common_data" {}

#-----------------------------------------------#
# Gets the default AWS data using primary       #
# provider for this account.                    #
#-----------------------------------------------#
data "aws_partition" "current" {
  provider = aws.{{ aws_region.name }}
}
data "aws_vpc" "available" {
  provider = aws.{{ aws_region.name }}
  state = "available"
}

#-----------------------------------------------#
# Modules Section.  Used to instantiate         #
# modules.                                      #
#-----------------------------------------------#
module "tfstate" {
  source          = "{{ role_path }}/terraform/modules/tfstate"
  providers       = { aws = aws.{{ aws_region.name }} }
  common_data     = merge ({
    aws_partition = data.aws_partition.current.partition
    vpc           = data.aws_vpc.available
    vpc_id        = data.aws_vpc.available.id
  }, jsondecode(var.common_data) ) # Ansible passes in common_data as JSON string so convert to TF object
}

#-------------------------------------------------------------------------------------------#
# Outputs Section.  Used to create variables usable in other modules and configurations.    #
#-------------------------------------------------------------------------------------------#
#output "aws_out" {
#  value = module.aws.aws_out
#}
