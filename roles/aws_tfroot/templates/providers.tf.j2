# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        providers.tf.j2                                                   #
# Version:                                                                        #
#               2024-01-25 WRC. Transformed to a Jinja template for Galaxy        #
#               2021-02-18 WRC. Initial                                           #
# Create Date:  2021-02-18                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#     Established the providers for this account and environment.  This was       #
#     split out as a separate resource file because its common across all         #
#     environments and may be automated in the future.                            #
#                                                                                 #
# ------------------------------------------------------------------------------- #

provider "aws" {
  alias                     = "{{ aws_common_vars.aws_default_region }}"
  region                    = "{{ aws_common_vars.aws_default_region }}"
  profile                   = "{{ aws_common_vars.aws_profile }}"
  shared_credentials_files  = [ "{{ aws_tfroot_tempdir.path }}/{{ aws_common_vars.aws_credentials_file }}" ]
  allowed_account_ids       = [ "{{ aws_common_facts.account }}" ]
  max_retries               = "{{ aws_common_vars.max_attempts }}"
  insecure                  = false
}
