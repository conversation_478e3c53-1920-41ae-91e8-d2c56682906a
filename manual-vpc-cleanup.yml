---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        manual-vpc-cleanup.yml                                            #
# Version:                                                                        #
#               2024-10-05 Manual VPC cleanup                                     #
# Create Date:  2024-10-05                                                        #
# Description:                                                                    #
#               Manually delete VPC resources using Ansible AWS modules          #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Manually cleanup VPC resources
  hosts: localhost
  gather_facts: false
  connection: local
  
  vars:
    target_environment: "dev"
    target_vpc_cidr: "10.0.0.0/16"
    
  tasks:
    # ------------------------------------------------------------------------------- #
    - name: Find VPCs with our CIDR block
      amazon.aws.ec2_vpc_info:
        region: us-east-1
        filters:
          cidr: "{{ target_vpc_cidr }}"
      register: target_vpcs

    # ------------------------------------------------------------------------------- #
    - name: Display target VPCs
      ansible.builtin.debug:
        msg: |
          Found {{ target_vpcs.vpcs | length }} VPCs with CIDR {{ target_vpc_cidr }}:
          {% for vpc in target_vpcs.vpcs %}
          - VPC ID: {{ vpc.vpc_id }}
            State: {{ vpc.state }}
            Tags: {{ vpc.tags | default({}) }}
          {% endfor %}

    # ------------------------------------------------------------------------------- #
    - name: Find subnets in target VPCs
      amazon.aws.ec2_subnet_info:
        region: us-east-1
        filters:
          vpc-id: "{{ item.vpc_id }}"
      register: vpc_subnets
      loop: "{{ target_vpcs.vpcs }}"
      when: target_vpcs.vpcs | length > 0

    # ------------------------------------------------------------------------------- #
    - name: Find Internet Gateways attached to target VPCs
      amazon.aws.ec2_internet_gateway_info:
        region: us-east-1
        filters:
          attachment.vpc-id: "{{ item.vpc_id }}"
      register: vpc_igws
      loop: "{{ target_vpcs.vpcs }}"
      when: target_vpcs.vpcs | length > 0

    # ------------------------------------------------------------------------------- #
    - name: Find route tables in target VPCs
      amazon.aws.ec2_route_table_info:
        region: us-east-1
        filters:
          vpc-id: "{{ item.vpc_id }}"
      register: vpc_route_tables
      loop: "{{ target_vpcs.vpcs }}"
      when: target_vpcs.vpcs | length > 0

    # ------------------------------------------------------------------------------- #
    - name: Confirm deletion
      ansible.builtin.pause:
        prompt: |
          
          ⚠️  WARNING: This will delete the following resources:
          
          VPCs: {{ target_vpcs.vpcs | length }}
          {% if vpc_subnets.results is defined %}
          Subnets: {{ vpc_subnets.results | map(attribute='subnets') | map('length') | sum }}
          {% endif %}
          {% if vpc_igws.results is defined %}
          Internet Gateways: {{ vpc_igws.results | map(attribute='internet_gateways') | map('length') | sum }}
          {% endif %}
          
          Type 'DELETE' to confirm deletion, or press Ctrl+C to cancel
      register: deletion_confirmation
      when: target_vpcs.vpcs | length > 0

    # ------------------------------------------------------------------------------- #
    - name: Delete subnets
      amazon.aws.ec2_subnet:
        region: us-east-1
        subnet_id: "{{ subnet.subnet_id }}"
        state: absent
      loop: "{{ vpc_subnets.results | map(attribute='subnets') | flatten }}"
      loop_control:
        loop_var: subnet
      when: 
        - target_vpcs.vpcs | length > 0
        - deletion_confirmation.user_input == 'DELETE'
        - vpc_subnets.results is defined
      ignore_errors: true

    # ------------------------------------------------------------------------------- #
    - name: Detach and delete Internet Gateways
      amazon.aws.ec2_internet_gateway:
        region: us-east-1
        internet_gateway_id: "{{ igw.internet_gateway_id }}"
        vpc_id: "{{ igw.attachments[0].vpc_id }}"
        state: absent
      loop: "{{ vpc_igws.results | map(attribute='internet_gateways') | flatten }}"
      loop_control:
        loop_var: igw
      when: 
        - target_vpcs.vpcs | length > 0
        - deletion_confirmation.user_input == 'DELETE'
        - vpc_igws.results is defined
        - igw.attachments | length > 0
      ignore_errors: true

    # ------------------------------------------------------------------------------- #
    - name: Delete custom route tables (not main)
      amazon.aws.ec2_route_table:
        region: us-east-1
        route_table_id: "{{ rt.route_table_id }}"
        state: absent
      loop: "{{ vpc_route_tables.results | map(attribute='route_tables') | flatten }}"
      loop_control:
        loop_var: rt
      when: 
        - target_vpcs.vpcs | length > 0
        - deletion_confirmation.user_input == 'DELETE'
        - vpc_route_tables.results is defined
        - not (rt.associations | selectattr('main', 'equalto', true) | list)
      ignore_errors: true

    # ------------------------------------------------------------------------------- #
    - name: Delete VPCs
      amazon.aws.ec2_vpc:
        region: us-east-1
        vpc_id: "{{ vpc.vpc_id }}"
        state: absent
      loop: "{{ target_vpcs.vpcs }}"
      loop_control:
        loop_var: vpc
      when: 
        - target_vpcs.vpcs | length > 0
        - deletion_confirmation.user_input == 'DELETE'
      ignore_errors: true

    # ------------------------------------------------------------------------------- #
    - name: Cleanup complete
      ansible.builtin.debug:
        msg: |
          VPC cleanup completed!
          {% if deletion_confirmation.user_input == 'DELETE' %}
          All resources have been deleted.
          {% else %}
          Cleanup was cancelled by user.
          {% endif %}
      when: target_vpcs.vpcs | length > 0

    # ------------------------------------------------------------------------------- #
    - name: No resources found
      ansible.builtin.debug:
        msg: "No VPCs found with CIDR {{ target_vpc_cidr }}. Nothing to clean up."
      when: target_vpcs.vpcs | length == 0
