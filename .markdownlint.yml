---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        .markdownlint.yml                                                 #
# Version:                                                                        #
#               2024-07-09 WRC. Initial                                           #
# Create Date:  2024-07-09                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               This is the configuration file for the markdownlint program.      #
#               The source project is https://github.com/DavidAnson/markdownlint  #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
# style "#{File.dirname(__FILE__)}/.mdlrules.rb"
ignore_front_matter: true
verbose: true
MD004: { style: "dash" }                                          # ul-style
MD005: true                                                       # Ordered list indent
MD007: { indent: 4 }                                              # ul-list-indent
MD013: false                                                      # line-length
MD024: { siblings_only: true }                                    # no-duplicate-heading
MD033: { allowed_elements: [a, br, center, img, ins, small] }   # no-inline-html
MD040: {                                                          # fenced-code-language
  allowed_languages: [bash, html, json, markdown, python, text, toml, yaml],
  language_only: true
}
MD046: { style: fenced }                                          # code-block-style
MD048: { style: backtick }                                        # code-fence-style
MD050: { style: asterisk }                                        # strong-style
