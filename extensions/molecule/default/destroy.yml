---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        destroy.yml                                                       #
# Version:                                                                        #
#               2024-08-08 Initial                                                #
# Create Date:  2024-08-08                                                        #
# Author:       <PERSON> (<EMAIL>)                                  #
# Description:                                                                    #
#               Initial commit for Molecule Destroy template                      #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: Cleanup
  hosts: localhost
  connection: local
  gather_facts: false
  tasks:
    - name: Remove Molecule Project
      ansible.builtin.include_tasks:
        file: "/roles/{{ molecule_project_name }}/remove.yml"

    - name: Delete Test Project
      ansible.builtin.command: "oc delete project {{ molecule_project_name }}"
      register: oc_del_proj
      changed_when: true
      failed_when: oc_del_proj.rc != 0
