---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        verify.yml                                                        #
# Version:                                                                        #
#               2024-08-08 Initial                                                #
# Create Date:  2024-08-08                                                        #
# Author:       <PERSON> (<EMAIL>)                                  #
# Description:                                                                    #
#               Initial commit for Molecule Converge template                     #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: Converge
  hosts: localhost
  tasks:
    - name: Create a project in OpenShift
      ansible.builtin.shell:
        cmd: |
          oc create -f - <<EOF
          apiVersion: project.openshift.io/v1
          kind: ProjectRequest
          metadata:
            name: "{{ molecule_project_name }}"
            displayName: "{{ molecule_project_displayname }}"
          EOF
      changed_when: true
      register: oc_proj_create
      failed_when: oc_proj_create.rc != 0
      args:
        executable: /bin/bash

    - name: Include target operator role
      ansible.builtin.include_role:
        name: "{{ target_operator }}"
        tasks_from: install.yml
        apply:
          delegate_to: localhost
