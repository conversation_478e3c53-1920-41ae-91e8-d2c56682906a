---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        prepare.yml                                                       #
# Version:                                                                        #
#               2024-08-08 Initial                                                #
# Create Date:  2024-08-08                                                        #
# Author:       <PERSON> (<EMAIL>)                                  #
# Description:                                                                    #
#               Initial commit for Molecule Prepare phase template                #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: Prepare
  hosts: all
  tasks:
    - name: Manage OpenShift Configuration File via Login
      when: not use_kube_file | bool
      block:
        - name: Login to OpenShift (Token)
          ansible.builtin.command: >
            oc login --insecure-skip-tls-verify=true {{ oc_master_url }} --token={{ oc_token }}
          when: not oc_token | trim == ""
          changed_when: true
          failed_when: oc_token_login.rc != 0

        - name: Login to OpenShift (Username/Password)
          ansible.builtin.command: >
            oc login --insecure-skip-tls-verify=true  --username={{ oc_username }} --password={{ oc_password }} {{ oc_master_url }}
          when: oc_token | trim == ""
          register: oc_user_pass_login
          changed_when: true
          failed_when: oc_user_pass_login.rc != 0
