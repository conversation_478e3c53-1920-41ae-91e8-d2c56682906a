---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        molecule.yml                                                      #
# Version:                                                                        #
#               2024-08-08 Initial                                                #
# Create Date:  2024-08-08                                                        #
# Author:       <PERSON> (<EMAIL>)                                  #
# Description:                                                                    #
#               Initial commit for Molecule template                              #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
dependency:
  name: galaxy
  options:
    requirements-file: requirements.yml
driver:
  name: default
  options:
    managed: false
    ansible_connection_options:
      ansible_connection: local
lint: |
  yamllint -c gitlab/.ansible-lint .
platforms:
  - name: instance
provisioner:
  name: ansible
  config_options:
    defaults:
      roles_path: ../
  inventory:
    group_vars:
      all:
        use_kube_file: "${USE_KUBE_FILE:-True}"
# Lines 41-46 are used in case ${USE_KUBE_FILE:-False} They are left here for reference for future communit developers.
#        oc_binary_url: "${OC_BINARY_URL:-https://mirror.openshift.com/pub/openshift-v3/clients/3.10.45/linux/oc.tar.gz}"
#        kube_binary_url: "${KUBE_BINARY:-https://storage.googleapis.com/kubernetes-release/release/v1.10.13/bin/linux/amd64/kubectl}"
#        oc_master_url: "${OC_MASTER_URL:-"
#        oc_username: "${OC_USERNAME:-admin}"
#        oc_password: "${OC_PASSWORD:-admin}"
#        oc_token: "${OC_TOKEN}"
        molecule_target_operator:
          chart_values:
            certmanager:
              install: false
            global:
              hosts:
                domain: "apps.{{ ocp.openshift_domain }}"
              ingress:
                annotations:
                  route.openshift.io/termination: edge
                class: openshift-default
                configureCertmanager: false
                tls:
                  secretName: wildcard-tls-cert
            nginx-ingress:
              enabled: false
          operator_hub:
            name: "{{ molecule_target_operator }}"
            namespace: "{{ molecule_project_name }}"
            channel: stable
            source: "{{ 'cs-certified-operator-index' if ocp.disconnected else 'certified-operators' }}"
            sourceNamespace: openshift-marketplace
            pod_label_check: 'control-plane=controller-manager'
            crd_check:
              - gitlabs.apps.gitlab.com
            all_namespaces: false
            add_resources: true
            wait_time: 30
        ocp:
          openshift_domain: " "
          # openshift_domain must be defined, but the value is overwritten by the kubeconfig when USE_KUBE_FILE:-True
          disconnected: true
        #  cluster:
        #    host: https://api.ocp-wtn1-lab00.ssc.lmco.com:6443
        #    api_key: sha256~UScP4iryruUarAaqN2dE_AO7V4pDYqL077m6cB_c6pg
  # lint:
  #  name: ansible-lint
  #  options:
  #    # Skipping certin rules per https://github.com/ansible/ansible-lint/blob/master/RULE_DOCS.md
  #    x:
  #      - 301
  #      - 305
scenario:
  name: default
  test_sequence:
    - verify
