---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        runtime.yml                                                       #
# Version:                                                                        #
#               2024-03-05 Initial                                                #
# Create Date:  2024-03-05                                                        #
# Author:       <PERSON><PERSON>, <PERSON> (<EMAIL>)                             #
# Description:                                                                    #
#               Initial commit for runtime.yml                                    #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

# ------------------------------------------------------------------------------- #
# Collections must specify a minimum required ansible version to upload           #
# to galaxy                                                                       #
# ------------------------------------------------------------------------------- #
requires_ansible: '>=2.16.11'

# ------------------------------------------------------------------------------- #
# Content that Ansible needs to load from another location or that has            #
# been deprecated/removed                                                         #
# ------------------------------------------------------------------------------- #
# plugin_routing:
#   action:

# ------------------------------------------------------------------------------- #
# Python import statements that Ansible needs to load from another location       #
# import_redirection:                                                             #
# ------------------------------------------------------------------------------- #
#   ansible_collections.ns.col.plugins.module_utils.old_location:
#     redirect: ansible_collections.ns.col.plugins.module_utils.new_location

# https://docs.ansible.com/ansible/latest/dev_guide/developing_collections_structure.html
