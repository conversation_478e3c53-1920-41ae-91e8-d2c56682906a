---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        check-vpc-resources.yml                                           #
# Version:                                                                        #
#               2024-10-05 VPC resource checker                                   #
# Create Date:  2024-10-05                                                        #
# Description:                                                                    #
#               Check what VPC resources exist in AWS                             #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Check existing VPC resources in AWS
  hosts: localhost
  gather_facts: false
  connection: local
  
  tasks:
    # ------------------------------------------------------------------------------- #
    - name: List all VPCs in the region
      amazon.aws.ec2_vpc_net_info:
        region: us-east-1
      register: vpc_list

    # ------------------------------------------------------------------------------- #
    - name: Display VPC information
      ansible.builtin.debug:
        msg: |
          Found {{ vpc_list.vpcs | length }} VPCs:
          {% for vpc in vpc_list.vpcs %}
          - VPC ID: {{ vpc.vpc_id }}
            CIDR: {{ vpc.cidr_block }}
            State: {{ vpc.state }}
            Tags: {{ vpc.tags | default({}) }}
          {% endfor %}

    # ------------------------------------------------------------------------------- #
    - name: List subnets for dev environment VPCs
      amazon.aws.ec2_subnet_info:
        region: us-east-1
        filters:
          "tag:Environment": "dev"
      register: subnet_list
      ignore_errors: true

    # ------------------------------------------------------------------------------- #
    - name: Display subnet information
      ansible.builtin.debug:
        msg: |
          Found {{ subnet_list.subnets | length }} subnets with Environment=dev:
          {% for subnet in subnet_list.subnets %}
          - Subnet ID: {{ subnet.subnet_id }}
            VPC ID: {{ subnet.vpc_id }}
            CIDR: {{ subnet.cidr_block }}
            AZ: {{ subnet.availability_zone }}
            Tags: {{ subnet.tags | default({}) }}
          {% endfor %}
      when: subnet_list.subnets is defined

    # ------------------------------------------------------------------------------- #
    - name: List Internet Gateways
      amazon.aws.ec2_internet_gateway_info:
        region: us-east-1
        filters:
          "tag:Environment": "dev"
      register: igw_list
      ignore_errors: true

    # ------------------------------------------------------------------------------- #
    - name: Display Internet Gateway information
      ansible.builtin.debug:
        msg: |
          Found {{ igw_list.internet_gateways | length }} Internet Gateways with Environment=dev:
          {% for igw in igw_list.internet_gateways %}
          - IGW ID: {{ igw.internet_gateway_id }}
            State: {{ igw.state }}
            Attachments: {{ igw.attachments }}
            Tags: {{ igw.tags | default({}) }}
          {% endfor %}
      when: igw_list.internet_gateways is defined
