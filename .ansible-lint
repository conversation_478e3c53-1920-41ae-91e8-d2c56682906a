---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        .ansible-lint.yml                                                 #
# Version:                                                                        #
#               2025-02-03. WRC. Add opt-in rules to increase quality.            #
# Create Date:  2023-07-08                                                        #
# Author:       <PERSON> <<EMAIL>>                      #
# Description:                                                                    #
#               Updated standard ansible lint file                                #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

# ------------------------------------------------------------------------------- #
# Ansible configuration values                                                    #
# ------------------------------------------------------------------------------- #
# End-goal is clean lint when using "production." Nothing below "safety" should be used
profile: production # min, basic, moderate, safety, shared, production

# ------------------------------------------------------------------------------- #
# exclude_paths included in this file are parsed relative to this file's location #
# and not relative to the CWD of execution. CLI arguments passed to the --exclude #
# option are parsed relative to the CWD of execution.                             #
# ------------------------------------------------------------------------------- #
exclude_paths:
  - .cache/ # implicit unless exclude_paths is defined in config
  - .gitlab-ci.yml # Need to skip this because of '!reference' usages
  - mkdocs.yml
  - roles/*/files

# parseable: true
# quiet: true
# strict: true
verbosity: 1

# ------------------------------------------------------------------------------- #
# Enforce variable names to follow pattern below, in addition to Ansible own      #
# requirements, like avoiding python identifiers. To disable add `var-naming`     #
# to skip_list.                                                                   #
# ------------------------------------------------------------------------------- #
var_naming_pattern: "^[a-z_][a-z0-9_]*$"
use_default_rules: true

# ------------------------------------------------------------------------------- #
# Enable, Warn and Skip lists                                                     #
# ------------------------------------------------------------------------------- #
enable_list:
  - empty-string-compare      # opt-in. This rule checks that the version key within galaxy.yml is greater than or equal to 1.0.0.
  - galaxy-version-incorrect  # opt-in. Version key within galaxy.yml is greater than or equal to 1.0.0
  - inline-env-var            # opt-in. Do not set environment variables in the ansible.builtin.command
  - meta-incorrect            # opt-in. Mtadata for fields with undefined or default values
  - name[prefix]              # opt-in. Use Task naming as "<task file> | <Task Name>" in non-main.yml files
  - no-log-password           # opt-in. Playbooks do not write passwords to logs when using loops
  - no-same-owner             # opt-in. Check owner and group do not transfer across hosts.
warn_list:
  - name[prefix]                # Make this a hard failure in future.
  - var-naming[no-role-prefix]  # Does not like include_role task variable.
  - var-naming[pattern]         # CamelCase for matching config options
skip_list:
  - yaml[commas]  # WRC. For data dict lists that define a table model, this warning
  - yaml[colons]  # should be ignored since is disallows aligned tables.
  - name[template] # should be ignored to allow variables in the task name for clearer descriptions

# ------------------------------------------------------------------------------- #
# Offline mode disables installation of requirements.yml and schema refreshing    #
# ------------------------------------------------------------------------------- #
# offline: true

# ------------------------------------------------------------------------------- #
# Uncomment to enforce action validation with tasks, usually is not               #
# needed as Ansible syntax check also covers it.                                  #
# ------------------------------------------------------------------------------- #
skip_action_validation: false
...
