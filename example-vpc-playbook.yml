---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        example-vpc-playbook.yml                                          #
# Version:                                                                        #
#               2024-10-05 Initial creation                                       #
# Create Date:  2024-10-05                                                        #
# Description:                                                                    #
#               Example playbook demonstrating how to use the aws_tfvpc role      #
#               to deploy VPC infrastructure using Terraform                     #
#                                                                                 #
# Usage:                                                                          #
#   ansible-playbook -i inventory example-vpc-playbook.yml                       #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Deploy AWS VPC Infrastructure using Terraform
  hosts: localhost
  gather_facts: true
  connection: local
  
  vars:
    # AWS Common Configuration
    aws_common_config:
      environment: "dev"
      region: "us-east-1"
      account: "************"  # Replace with your AWS account ID
      
    # AWS Credentials Configuration
    aws_common_credentials:
      profile: "default"  # AWS CLI profile to use
      max_retries: 50
      insecure: false
      # Uncomment and modify if using specific credentials file
      # shared_credentials_files: ["/path/to/credentials"]
      # allowed_account_ids: ["************"]
    
    # VPC Configuration
    aws_tfvpc_config:
      environment: "dev"
      vpc_init: true
      
      # Network Configuration
      vpc_cidr_block: "10.0.0.0/16"
      public_subnet_cidr: "********/24"
      availability_zone: "us-east-1a"  # Leave empty for auto-selection
      
      # VPC Settings
      enable_dns_support: true
      enable_dns_hostnames: true
      map_public_ip_on_launch: true
      
      # Resource Naming (optional overrides)
      vpc_name_override: ""
      subnet_name_override: ""
      
      # Additional Tags
      additional_tags:
        Project: "VPC-Demo"
        Owner: "DevOps-Team"
        CostCenter: "Engineering"
      
      # Terraform Execution Control
      terraform_init: true
      terraform_plan: true
      terraform_apply: true
      terraform_destroy: false  # Set to true to destroy infrastructure
      
      # State Management (optional - for remote state)
      use_remote_state: false
      # state_bucket: "my-terraform-state-bucket"
      # state_key: "vpc/dev/terraform.tfstate"
      # state_region: "us-east-1"
      # state_dynamodb_table: "terraform-state-lock"
      
      # Cleanup
      cleanup_temp_files: true

  tasks:
    # ------------------------------------------------------------------------------- #
    # - name: Include AWS Common role for shared configuration
    #   ansible.builtin.include_role:
    #     name: aws_common
    #   vars:
    #     aws_common_config: "{{ aws_common_config }}"

    # ------------------------------------------------------------------------------- #
    - name: Deploy VPC infrastructure using aws_tfvpc role
      ansible.builtin.include_role:
        name: aws_tfvpc
      vars:
        aws_tfvpc_config: "{{ aws_tfvpc_config }}"

    # ------------------------------------------------------------------------------- #
    - name: Display VPC deployment results
      ansible.builtin.debug:
        msg: |
          VPC Deployment Results:
          ======================
          VPC ID: {{ aws_tfvpc_outputs.vpc_id.value | default('N/A') }}
          VPC CIDR: {{ aws_tfvpc_outputs.vpc_cidr_block.value | default('N/A') }}
          Public Subnet ID: {{ aws_tfvpc_outputs.public_subnet_id.value | default('N/A') }}
          Public Subnet CIDR: {{ aws_tfvpc_outputs.public_subnet_cidr_block.value | default('N/A') }}
          Internet Gateway ID: {{ aws_tfvpc_outputs.internet_gateway_id.value | default('N/A') }}
          Route Table ID: {{ aws_tfvpc_outputs.public_route_table_id.value | default('N/A') }}
          Availability Zone: {{ aws_tfvpc_outputs.availability_zone.value | default('N/A') }}
      when: aws_tfvpc_outputs is defined

    # ------------------------------------------------------------------------------- #
    - name: Save VPC outputs to file
      ansible.builtin.copy:
        content: |
          # VPC Deployment Outputs
          # Generated: {{ ansible_date_time.iso8601 }}
          
          VPC_ID="{{ aws_tfvpc_outputs.vpc_id.value | default('') }}"
          VPC_CIDR="{{ aws_tfvpc_outputs.vpc_cidr_block.value | default('') }}"
          PUBLIC_SUBNET_ID="{{ aws_tfvpc_outputs.public_subnet_id.value | default('') }}"
          PUBLIC_SUBNET_CIDR="{{ aws_tfvpc_outputs.public_subnet_cidr_block.value | default('') }}"
          INTERNET_GATEWAY_ID="{{ aws_tfvpc_outputs.internet_gateway_id.value | default('') }}"
          PUBLIC_ROUTE_TABLE_ID="{{ aws_tfvpc_outputs.public_route_table_id.value | default('') }}"
          AVAILABILITY_ZONE="{{ aws_tfvpc_outputs.availability_zone.value | default('') }}"
        dest: "./vpc-outputs.env"
        mode: '0644'
      when: aws_tfvpc_outputs is defined

# ------------------------------------------------------------------------------- #
# Example of how to use the VPC outputs in subsequent plays                      #
# ------------------------------------------------------------------------------- #
- name: Example - Use VPC outputs for additional resources
  hosts: localhost
  gather_facts: false
  connection: local
  
  tasks:
    - name: Load VPC outputs
      ansible.builtin.include_vars:
        file: "./vpc-outputs.env"
        name: vpc_env
      when: ansible_run_tags is not defined or 'vpc-only' not in ansible_run_tags

    - name: Example task using VPC outputs
      ansible.builtin.debug:
        msg: |
          You can now use the VPC outputs for deploying additional resources:
          - Deploy EC2 instances in subnet: {{ vpc_env.PUBLIC_SUBNET_ID | default('N/A') }}
          - Configure security groups for VPC: {{ vpc_env.VPC_ID | default('N/A') }}
          - Set up load balancers, etc.
      when: 
        - vpc_env is defined
        - ansible_run_tags is not defined or 'vpc-only' not in ansible_run_tags
