---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        destroy-vpc-playbook.yml                                          #
# Version:                                                                        #
#               2024-10-05 VPC destruction playbook                               #
# Create Date:  2024-10-05                                                        #
# Description:                                                                    #
#               Simple playbook to destroy VPC infrastructure                     #
#                                                                                 #
# Usage:                                                                          #
#   ansible-playbook destroy-vpc-playbook.yml                                     #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Destroy AWS VPC Infrastructure using Terraform
  hosts: localhost
  gather_facts: true
  connection: local
  
  vars:
    # VPC Configuration for destruction
    aws_tfvpc_config:
      # AWS Settings
      account: "************"  # Your AWS account ID
      region: "us-east-1"      # AWS region
      aws_profile: "default"   # AWS CLI profile
      
      # Basic Settings
      environment: "dev"
      vpc_init: true
      
      # Network Configuration (needed for state reference)
      vpc_cidr_block: "10.0.0.0/16"
      public_subnet_cidr: "********/24"
      availability_zone: "us-east-1a"
      
      # VPC Settings
      enable_dns_support: true
      enable_dns_hostnames: true
      map_public_ip_on_launch: true
      
      # Additional Tags
      additional_tags:
        Project: "VPC-Demo"
        Owner: "DevOps-Team"
        Environment: "dev"
      
      # Terraform Execution Control - DESTROY MODE
      terraform_init: true
      terraform_plan: false
      terraform_apply: false
      terraform_destroy: true  # This will destroy the infrastructure
      
      # Cleanup
      cleanup_temp_files: true

  tasks:
    # ------------------------------------------------------------------------------- #
    - name: Destroy VPC infrastructure using aws_tfvpc role
      ansible.builtin.include_role:
        name: aws_tfvpc

    # ------------------------------------------------------------------------------- #
    - name: Display destruction results
      ansible.builtin.debug:
        msg: |
          VPC Destruction Results:
          =======================
          {% if aws_tfvpc_destroy_result is defined %}
          Destruction Status: {{ 'SUCCESS' if aws_tfvpc_destroy_result.rc == 0 else 'FAILED' }}
          Return Code: {{ aws_tfvpc_destroy_result.rc }}
          {% else %}
          Destruction Status: SKIPPED or NOT EXECUTED
          {% endif %}
      when: aws_tfvpc_destroy_result is defined or aws_tfvpc_vars.terraform_destroy | default(false)

    # ------------------------------------------------------------------------------- #
    - name: Cleanup outputs file
      ansible.builtin.file:
        path: "./vpc-outputs.env"
        state: absent
      ignore_errors: true
