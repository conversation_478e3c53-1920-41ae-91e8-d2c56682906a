#!/bin/bash
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        test-vpc-deployment.sh                                            #
# Version:                                                                        #
#               2024-10-05 Initial creation                                       #
# Create Date:  2024-10-05                                                        #
# Description:                                                                    #
#               Test script for validating VPC deployment using aws_tfvpc role    #
#                                                                                 #
# Usage:                                                                          #
#   ./test-vpc-deployment.sh [plan|apply|destroy]                                #
#                                                                                 #
# ------------------------------------------------------------------------------- #

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if ansible is installed
    if ! command -v ansible-playbook &> /dev/null; then
        print_error "ansible-playbook is not installed or not in PATH"
        exit 1
    fi
    
    # Check if terraform is installed
    if ! command -v terraform &> /dev/null; then
        print_error "terraform is not installed or not in PATH"
        exit 1
    fi
    
    # Check if AWS CLI is configured
    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS CLI is not configured or credentials are invalid"
        exit 1
    fi
    
    print_success "All prerequisites are met"
}

# Function to validate file structure
validate_structure() {
    print_status "Validating file structure..."
    
    local required_files=(
        "roles/aws_tfvpc/tasks/main.yml"
        "roles/aws_tfvpc/meta/argument_specs.yml"
        "roles/aws_tfvpc/defaults/main/aws_tfvpc_defaults.yml"
        "terraform/modules/vpc/vpc_main.tf"
        "terraform/modules/vpc/variables.tf"
        "terraform/modules/vpc/outputs.tf"
        "example-vpc-playbook.yml"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            print_error "Required file not found: $file"
            exit 1
        fi
    done
    
    print_success "File structure validation passed"
}

# Function to run syntax check
syntax_check() {
    print_status "Running Ansible syntax check..."
    
    if ansible-playbook --syntax-check example-vpc-playbook.yml; then
        print_success "Ansible syntax check passed"
    else
        print_error "Ansible syntax check failed"
        exit 1
    fi
}

# Function to run the playbook
run_playbook() {
    local action=$1
    print_status "Running VPC deployment with action: $action"
    
    case $action in
        "plan")
            print_status "Running Terraform plan only..."
            ansible-playbook example-vpc-playbook.yml \
                -e "aws_tfvpc_config.terraform_apply=false" \
                -e "aws_tfvpc_config.terraform_destroy=false" \
                -v
            ;;
        "apply")
            print_status "Running full VPC deployment..."
            ansible-playbook example-vpc-playbook.yml \
                -e "aws_tfvpc_config.terraform_destroy=false" \
                -v
            ;;
        "destroy")
            print_warning "This will destroy the VPC infrastructure!"
            read -p "Are you sure you want to continue? (yes/no): " confirm
            if [[ $confirm == "yes" ]]; then
                ansible-playbook example-vpc-playbook.yml \
                    -e "aws_tfvpc_config.terraform_apply=false" \
                    -e "aws_tfvpc_config.terraform_destroy=true" \
                    -v
            else
                print_status "Destroy operation cancelled"
                exit 0
            fi
            ;;
        *)
            print_error "Invalid action: $action"
            echo "Usage: $0 [plan|apply|destroy]"
            exit 1
            ;;
    esac
}

# Main execution
main() {
    local action=${1:-"plan"}
    
    echo "======================================"
    echo "AWS VPC Deployment Test Script"
    echo "======================================"
    
    check_prerequisites
    validate_structure
    syntax_check
    run_playbook "$action"
    
    print_success "Test script completed successfully!"
}

# Run main function with all arguments
main "$@"
