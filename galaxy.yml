---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        galaxy.yml                                                        #
# Version:                                                                        #
#               2024-03-05 Initial                                                #
# Create Date:  2024-03-05                                                        #
# Author:       <PERSON><PERSON>, <PERSON> (<EMAIL>)                             #
# Description:                                                                    #
#               Initial commit for galaxy.yml                                     #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

# Reference: https://docs.ansible.com/ansible/latest/dev_guide/collections_galaxy_meta.html
# ------------------------------------------------------------------------------- #
#               *** Required by Ansible for ALL Collections ***                   #
# ------------------------------------------------------------------------------- #
#   namespace: can be a company/brand/organization or product namespace under     #
#     which all# content lives. May only contain alphanumeric lowercase chars     #
#     and underscores. Namespaces cannot start with underscores or numbers and    #
#     cannot contain consecutive underscores                                      #
#   name: name of the collection. Has the same char restrictions as 'namespace'   #
#   version: 1.0.3
#   readme: path relative path to the Markdown (.md) readme file.                 #
#   authors: collection's content authors in the format 'Full Name <email>'       #
# ------------------------------------------------------------------------------- #
namespace: lmco
name: aws
version: 1.0.3
readme: README.md
authors:
  - Galaxy Automation Team
  - dl-EIT, Galaxy <<EMAIL>>

# ------------------------------------------------------------------------------- #
#         *** Required by LMCO Galaxy Automation for ALL Collections ***          #
# ------------------------------------------------------------------------------- #

# ------------------------------------------------------------------------------- #
# A short DESCRIPTIVE summary description of the collection.                      #
# ------------------------------------------------------------------------------- #
description: Collection of Amazon Web Services Assets

# ------------------------------------------------------------------------------- #
# 2013-08-11 WRC. DO NOT USE.                                                     #
# Either a single license or a list of licenses for content inside of a           #
# collection. Ansible Galaxy currently only accepts SPDX licenses. This key is    #
# mutually exclusive with license_file. Ansible Galaxy currently only accepts     #
# SPDX licenses. This key is mutually exclusive with license_file.                #
# ------------------------------------------------------------------------------- #
# license:

# ------------------------------------------------------------------------------- #
# 2013-08-11 WRC. ALWAYS USE "LICENSE"                                            #
# The path to the license file for the collection. This path is relative to the   #
# root of the collection. This key is mutually exclusive with license.            #
# ------------------------------------------------------------------------------- #
license_file: LICENSE

# ------------------------------------------------------------------------------- #
# 2013-08-11 WRC. USE MEANINGFUL TAGS THAT INCLUDE "lmco", "galaxy", <component>  #
# A list of tags you want to associate with the collection for indexing and       #
# searching. A tag name has the same character requirements as namespace/name.    #
# ------------------------------------------------------------------------------- #
tags:
  - lmco
  - galaxy
  - automation
  - collection
  - infrastructure

# ------------------------------------------------------------------------------- #
# 2013-08-11 WRC. CRITICAL TO GET RIGHT. Use <namespace>.<collection>             #
# Collections that this collection requires to be installed for it to be usable.  #
# The key of the dict is the collection label namespace.name. The value is a      #
# version range specifiers. Multiple version range specifiers can be set and      #
# are separated by ,.                                                             #
# ------------------------------------------------------------------------------- #
dependencies: {}

# ------------------------------------------------------------------------------- #
# The URL of the originating SCM repository.                                      #
# ------------------------------------------------------------------------------- #
repository: https://gitlab.global.lmco.com/galaxy/ansible/collections/lmco/galaxy

# ------------------------------------------------------------------------------- #
# The URL to the online LMCO Galaxy docs for this collection. Use 'docs' dir      #
# ------------------------------------------------------------------------------- #
documentation: https://gitlab.global.lmco.com/galaxy/ansible/collections/lmco/ansible/-/tree/main/docs

# ------------------------------------------------------------------------------- #
# The URL to the homepage of the LMCO Galaxy GitLab collection project.           #
# ------------------------------------------------------------------------------- #
homepage: https://gitlab.global.lmco.com/galaxy/ansible/collections/lmco/galaxy

# ------------------------------------------------------------------------------- #
# The URL to the collection issue tracker                                         #
# ------------------------------------------------------------------------------- #
issues: https://ebstools-jira.us.lmco.com/projects/XBA_GALAXY_AUTO

# ------------------------------------------------------------------------------- #
#                *** New Collection Keys Currently Not Used ***                   #
# ------------------------------------------------------------------------------- #

# ------------------------------------------------------------------------------- #
# A list of file glob-like patterns used to filter any files or directories       #
# that should not be included in the build artifact. A pattern is matched from    #
# the relative path of the file or directory of the collection directory.         #
# This uses fnmatch to match the files or directories. Some directories and       #
# files like galaxy.yml, *.pyc, *.retry, and .git are always filtered. Mutually   #
# exclusive with manifest.                                                        #
# ------------------------------------------------------------------------------- #
build_ignore:
  - ".pre-commit-config.yaml"
  - ".editorconfig"
  - "molecule"
  - ".cache"
  - ".yamllint"
  - ".gitlab-ci.yml"

# ------------------------------------------------------------------------------- #
# A dict controlling use of manifest directives used in building the collection   #
# artifact. The key directives is a list of MANIFEST.in style directives.         #
# The key omit_default_directives is a boolean that controls whether the          #
# default directives are used. Mutually exclusive with build_ignore               #
# ------------------------------------------------------------------------------- #
# manifest: TBD
